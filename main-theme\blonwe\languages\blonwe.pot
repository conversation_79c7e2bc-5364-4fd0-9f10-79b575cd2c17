#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Blonwe\n"
"POT-Creation-Date: 2024-06-12 14:10+0300\n"
"PO-Revision-Date: 2024-06-12 14:09+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: style.css\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: includes\\merlin\\vendor\n"

#: 404.php:17
msgid "That Page Cant Be Found"
msgstr ""

#: 404.php:19
msgid ""
"It looks like nothing was found at this location. Maybe try to search for "
"what you are looking for?"
msgstr ""

#: 404.php:21
msgid "Go To Homepage"
msgstr ""

#: archive.php:44 archive.php:67 archive.php:91 archive.php:119 index.php:41
#: index.php:61 index.php:82 index.php:107 search.php:44 search.php:67
#: search.php:91 search.php:119 single.php:41 single.php:61 single.php:82
#: single.php:107
msgid "No Posts Found"
msgstr ""

#: comments.php:29
#, php-format
msgid "One thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:35 comments.php:58
msgid "Comment navigation"
msgstr ""

#: comments.php:36 comments.php:59
msgid "&larr; Older Comments"
msgstr ""

#: comments.php:37 comments.php:60
msgid "Newer Comments &rarr;"
msgstr ""

#: comments.php:71
msgid "Comments are closed."
msgstr ""

#: comments.php:77
msgid "You must be"
msgstr ""

#: comments.php:77
msgid "logged in"
msgstr ""

#: comments.php:77
msgid "to post a comment."
msgstr ""

#: dokan/store-sidebar.php:13 woocommerce/archive-product.php:132
#: woocommerce/archive-product.php:183 woocommerce/archive-product.php:237
#: woocommerce/archive-product.php:290
msgid "Filter Products"
msgstr ""

#: dokan/store.php:71
msgid "No products were found of this vendor!"
msgstr ""

#: functions.php:302
msgid "Meta Box"
msgstr ""

#: functions.php:307
msgid "Contact Form 7"
msgstr ""

#: functions.php:312
msgid "Kirki"
msgstr ""

#: functions.php:317
msgid "MailChimp Subscribe"
msgstr ""

#: functions.php:322
msgid "Elementor"
msgstr ""

#: functions.php:328
msgid "WooCommerce"
msgstr ""

#: functions.php:334
msgid "Blonwe Core"
msgstr ""



#: functions.php:379
msgid "Primary Navigation Menu"
msgstr ""

#: functions.php:387
msgid "Canvas Bottom Menu"
msgstr ""

#: functions.php:391
msgid "Top Left Menu"
msgstr ""

#: functions.php:395
msgid "Top Right Menu"
msgstr ""

#: functions.php:399
msgid "Sidebar Menu"
msgstr ""

#: functions.php:411
msgid "Read More"
msgstr ""

#: functions.php:429
msgid "Blog Sidebar"
msgstr ""

#: functions.php:431
msgid "These are widgets for the Blog page."
msgstr ""

#: functions.php:439
msgid "Shop Sidebar"
msgstr ""

#: functions.php:441
msgid "These are widgets for the Shop."
msgstr ""

#: functions.php:449
msgid "Shop Top Widget"
msgstr ""

#: functions.php:451
msgid "These are top widgets for the Shop."
msgstr ""

#: functions.php:459
msgid "Footer First Column"
msgstr ""

#: functions.php:461 functions.php:471 functions.php:481 functions.php:491
#: functions.php:501
msgid "These are widgets for the Footer."
msgstr ""

#: functions.php:469
msgid "Footer Second Column"
msgstr ""

#: functions.php:479
msgid "Footer Third Column"
msgstr ""

#: functions.php:489
msgid "Footer Fourth Column"
msgstr ""

#: functions.php:499
msgid "Footer Fifth Column"
msgstr ""

#: functions.php:534
msgid "Pingback:"
msgstr ""

#: functions.php:534
msgid "(Edit)"
msgstr ""

#: functions.php:558
msgid "Your comment is awaiting moderation."
msgstr ""

#: includes/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: includes/class-tgm-plugin-activation.php:335
#: includes/merlin/merlin-config.php:81
msgid "Install Plugins"
msgstr ""

#: includes/class-tgm-plugin-activation.php:337
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:339
#, php-format
msgid "Updating Plugin: %s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#: includes/class-tgm-plugin-activation.php:343
#, php-format
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:349
#, php-format
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:355
#, php-format
msgid ""
"The following plugin needs to be updated to its latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgid_plural ""
"The following plugins need to be updated to their latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:361
#, php-format
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:367
#, php-format
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:373
#, php-format
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:378
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:383
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:388
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: includes/class-tgm-plugin-activation.php:393
#: includes/class-tgm-plugin-activation.php:912
#: includes/class-tgm-plugin-activation.php:2618
#: includes/class-tgm-plugin-activation.php:3665
msgid "Return to the Dashboard"
msgstr ""

#: includes/class-tgm-plugin-activation.php:394
#: includes/class-tgm-plugin-activation.php:3244
msgid "Plugin activated successfully."
msgstr ""

#: includes/class-tgm-plugin-activation.php:395
#: includes/class-tgm-plugin-activation.php:3037
msgid "The following plugin was activated successfully:"
msgid_plural "The following plugins were activated successfully:"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:397
#, php-format
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#: includes/class-tgm-plugin-activation.php:399
#, php-format
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""

#: includes/class-tgm-plugin-activation.php:401
#, php-format
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: includes/class-tgm-plugin-activation.php:403
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""

#: includes/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: includes/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: includes/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: includes/class-tgm-plugin-activation.php:1019
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""

#: includes/class-tgm-plugin-activation.php:1019
#: includes/class-tgm-plugin-activation.php:1022
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""

#: includes/class-tgm-plugin-activation.php:1022
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""

#: includes/class-tgm-plugin-activation.php:1206
#: includes/class-tgm-plugin-activation.php:3033
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2067
#, php-format
msgid "TGMPA v%s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2358
#: includes/merlin/class-merlin.php:1117
msgid "Required"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2361
msgid "Recommended"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2377
msgid "WordPress Repository"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2380
msgid "External Source"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2383
msgid "Pre-Packaged"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2400
msgid "Not Installed"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2404
msgid "Installed But Not Activated"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2406
msgid "Active"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2412
msgid "Required Update not Available"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2415
msgid "Requires Update"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2418
msgid "Update recommended"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2427
#, php-format
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2473
#, php-format
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:2477
#, php-format
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:2481
#, php-format
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:2485
#, php-format
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/class-tgm-plugin-activation.php:2567
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2575
msgid "Installed version:"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2583
msgid "Minimum required version:"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2595
msgid "Available version:"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2618
msgid "No plugins to install, update or activate."
msgstr ""

#: includes/class-tgm-plugin-activation.php:2632
msgid "Plugin"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2633
msgid "Source"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2634
msgid "Type"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2638
msgid "Version"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2639
msgid "Status"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2688
#, php-format
msgid "Install %2$s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2693
#, php-format
msgid "Update %2$s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2699
#, php-format
msgid "Activate %2$s"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2769
msgid "Upgrade message from the plugin author:"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2802
#: includes/merlin/merlin-config.php:49 includes/merlin/merlin-config.php:50
#: includes/merlin/merlin-config.php:51
msgid "Install"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2808
msgid "Update"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2811
#: includes/merlin/merlin-config.php:53
msgid "Activate"
msgstr ""

#: includes/class-tgm-plugin-activation.php:2842
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: includes/class-tgm-plugin-activation.php:2844
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: includes/class-tgm-plugin-activation.php:2885
msgid "No plugins are available to be installed at this time."
msgstr ""

#: includes/class-tgm-plugin-activation.php:2887
msgid "No plugins are available to be updated at this time."
msgstr ""

#: includes/class-tgm-plugin-activation.php:2993
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3019
msgid "No plugins are available to be activated at this time."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3243
msgid "Plugin activation failed."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3583
#, php-format
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: includes/class-tgm-plugin-activation.php:3586
#, php-format
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3588
#, php-format
msgid "The installation of %1$s failed."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3592
msgid ""
"The installation and activation process is starting. This process may take a "
"while on some hosts, so please be patient."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3594
#, php-format
msgid "%1$s installed and activated successfully."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3594
#: includes/class-tgm-plugin-activation.php:3602
msgid "Show Details"
msgstr ""

#: includes/class-tgm-plugin-activation.php:3594
#: includes/class-tgm-plugin-activation.php:3602
msgid "Hide Details"
msgstr ""

#: includes/class-tgm-plugin-activation.php:3595
msgid "All installations and activations have been completed."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3597
#, php-format
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: includes/class-tgm-plugin-activation.php:3600
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3602
#, php-format
msgid "%1$s installed successfully."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3603
msgid "All installations have been completed."
msgstr ""

#: includes/class-tgm-plugin-activation.php:3605
#, php-format
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: includes/footer/footer-type1.php:103 includes/footer/footer-type2.php:102
msgid "app"
msgstr ""

#: includes/footer/footer-type1.php:122 includes/footer/footer-type2.php:121
msgid "Copyright 2022. All rights reserved"
msgstr ""

#: includes/footer/footer-type1.php:133 includes/footer/footer-type2.php:132
msgid "payment"
msgstr ""

#: includes/header/header-type1.php:58 includes/header/header-type2.php:59
#: includes/header/header-type3.php:59 includes/header/header-type4.php:58
#: includes/header/header-type5.php:58 includes/header/header-type6.php:58
msgid "Dark Theme"
msgstr ""

#: includes/header/header-type1.php:59 includes/header/header-type2.php:60
#: includes/header/header-type3.php:60 includes/header/header-type4.php:59
#: includes/header/header-type5.php:59 includes/header/header-type6.php:59
msgid "Light Theme"
msgstr ""

#: includes/header/header-type2.php:115
msgid "Deliver to"
msgstr ""

#: includes/header/header-type2.php:117
msgid "Select Location"
msgstr ""

#: includes/header/models/account-icon.php:21
#: includes/header/models/account-icon.php:39
#: includes/header/models/account-icon.php:68
#: includes/merlin/class-merlin.php:718
msgid "Welcome"
msgstr ""

#: includes/header/models/account-icon.php:24
#: includes/header/models/account-icon.php:42
#: includes/header/models/account-icon.php:71
msgid "Sign In"
msgstr ""

#: includes/header/models/account-icon.php:25
#: includes/header/models/account-icon.php:43
#: includes/header/models/account-icon.php:72
msgid "Account"
msgstr ""

#: includes/header/models/account-icon.php:55
#: woocommerce/myaccount/navigation.php:28
msgid "My Account"
msgstr ""

#: includes/header/models/account-icon.php:105
#: includes/header/models/account-icon.php:138
#: woocommerce/myaccount/form-login.php:69
#: woocommerce/myaccount/form-login.php:158
msgid "Log in"
msgstr ""

#: includes/header/models/account-icon.php:122
#: woocommerce/myaccount/form-login.php:47
#: woocommerce/myaccount/form-login.php:143
msgid "Username or email address"
msgstr ""

#: includes/header/models/account-icon.php:126
#: woocommerce/myaccount/form-login.php:51
#: woocommerce/myaccount/form-login.php:100
#: woocommerce/myaccount/form-login.php:147
#: woocommerce/myaccount/form-login.php:197
msgid "Password"
msgstr ""

#: includes/header/models/account-icon.php:134
#: woocommerce/myaccount/form-login.php:60
#: woocommerce/myaccount/form-login.php:155
msgid "Remember me"
msgstr ""

#: includes/header/models/account-icon.php:143
#: woocommerce/myaccount/form-login.php:64
#: woocommerce/myaccount/form-login.php:161
msgid "Lost your password?"
msgstr ""

#: includes/header/models/account-icon.php:153
msgid ""
"By continuing, you accept the Website Regulations , Regulations for the sale "
"of alcoholic beverages and the"
msgstr ""

#: includes/header/models/account-icon.php:159
msgid "You dont have an account yet?"
msgstr ""

#: includes/header/models/account-icon.php:159
msgid "Register Now"
msgstr ""

#: includes/header/models/canvas-menu.php:29
msgid "Main Menu"
msgstr ""

#: includes/header/models/canvas-menu.php:47
#: includes/header/models/sidebar-menu.php:12
msgid "Browse Categories"
msgstr ""

#: includes/header/models/canvas-menu.php:109
msgid "Copyright 2021. All rights reserved"
msgstr ""

#: includes/header/models/cart.php:15 includes/header/models/cart.php:40
#: includes/header/models/cart.php:66 includes/header/models/cart.php:92
#: includes/header/models/cart.php:121 includes/woocommerce.php:450
#: includes/woocommerce/product-type/product-type-list.php:65
#: includes/woocommerce/product-type/product-type-list2.php:56
#: includes/woocommerce/product-type/product-type10.php:80
#: includes/woocommerce/product-type/product-type13.php:76
#: includes/woocommerce/product-type/product-type2.php:83
#: includes/woocommerce/product-type/product-type2.php:94
#: includes/woocommerce/product-type/product-type3.php:85
#: includes/woocommerce/product-type/product-type3.php:98
#: includes/woocommerce/product-type/product-type4.php:77
#: includes/woocommerce/product-type/product-type5.php:82
#: includes/woocommerce/product-type/product-type6.php:77
#: includes/woocommerce/product-type/product-type8.php:91
#: includes/woocommerce/product-type/product-type8.php:102
#: includes/woocommerce/product-type/product-type9.php:78
#, php-format
msgid "%d"
msgid_plural "%d"
msgstr[0] ""
msgstr[1] ""

#: includes/header/models/cart.php:95
msgid "My Cart"
msgstr ""

#: includes/header/models/cart.php:124
msgid "Total"
msgstr ""

#: includes/header/models/search-holder.php:11
msgid "Type a few things below to search"
msgstr ""

#: includes/header/models/search-holder.php:13 includes/woocommerce.php:527
#: includes/woocommerce.php:535 includes/woocommerce.php:543
#: includes/woocommerce.php:551 includes/woocommerce.php:559
#: includes/woocommerce.php:577
msgid "Search for products..."
msgstr ""

#: includes/header/models/search-holder.php:24
#, php-format
msgid "Out of a total of %s products:"
msgstr ""

#: includes/header/models/search-holder.php:32 includes/woocommerce.php:587
msgid "Trending:"
msgstr ""

#: includes/header/models/toggle-menu-button.php:14
msgid "Menu"
msgstr ""

#: includes/header/models/top-notification-count.php:5
msgid "Expired"
msgstr ""

#: includes/header/models/top-notification-count.php:7
#: includes/header/models/top-notification-count.php:12
#: includes/header/models/top-notification-count.php:17
#: includes/header/models/top-notification-count.php:22
msgid "00"
msgstr ""

#: includes/header/models/top-notification-count.php:8
msgid "d"
msgstr ""

#: includes/header/models/top-notification-count.php:13
msgid "h"
msgstr ""

#: includes/header/models/top-notification-count.php:18
msgid "m"
msgstr ""

#: includes/header/models/top-notification-count.php:23
msgid "s"
msgstr ""

#: includes/header/models/wishlist-icon.php:18
msgid "Wishlist"
msgstr ""

#: includes/merlin/class-merlin.php:458
msgid "Something went wrong. Please refresh the page and try again!"
msgstr ""

#: includes/merlin/class-merlin.php:604
msgid "Please define default parameters in the form of an array."
msgstr ""

#: includes/merlin/class-merlin.php:609
msgid "Please define an SVG icon filename."
msgstr ""

#: includes/merlin/class-merlin.php:725
msgid "Child"
msgstr ""

#: includes/merlin/class-merlin.php:731
msgid "License"
msgstr ""

#: includes/merlin/class-merlin.php:739
msgid "Plugins"
msgstr ""

#: includes/merlin/class-merlin.php:747 includes/merlin/class-merlin.php:2033
msgid "Content"
msgstr ""

#: includes/merlin/class-merlin.php:753
msgid "Ready"
msgstr ""

#: includes/merlin/class-merlin.php:858
msgid "The welcome step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:956
msgid "The license activation step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:1026
msgid "The child theme installation step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:1034 includes/merlin/class-merlin.php:1166
msgid "Please verify the purchase code first."
msgstr ""

#: includes/merlin/class-merlin.php:1118
msgid "req"
msgstr ""

#: includes/merlin/class-merlin.php:1158
msgid "The plugin installation step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:1213
msgid "Select Demo"
msgstr ""

#: includes/merlin/class-merlin.php:1251
msgid "The content import step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:1331
msgid "The final step has been displayed"
msgstr ""

#: includes/merlin/class-merlin.php:1408
msgid "The existing child theme was activated"
msgstr ""

#: includes/merlin/class-merlin.php:1425
msgid "The newly generated child theme was activated"
msgstr ""

#: includes/merlin/class-merlin.php:1446
msgid ""
"Yikes! The theme activation failed. Please try again or contact support."
msgstr ""

#: includes/merlin/class-merlin.php:1455
msgid "Please add your license key before attempting to activate one."
msgstr ""

#: includes/merlin/class-merlin.php:1535 includes/merlin/class-merlin.php:1576
msgid "An error occurred, please try again."
msgstr ""

#: includes/merlin/class-merlin.php:1548
#, php-format
msgid "Your license key expired on %s."
msgstr ""

#: includes/merlin/class-merlin.php:1554
msgid "Your license key has been disabled."
msgstr ""

#: includes/merlin/class-merlin.php:1558
msgid ""
"This appears to be an invalid license key. Please try again or contact "
"support."
msgstr ""

#: includes/merlin/class-merlin.php:1563
msgid "Your license is not active for this URL."
msgstr ""

#: includes/merlin/class-merlin.php:1568
#, php-format
msgid "This appears to be an invalid license key for %s."
msgstr ""

#: includes/merlin/class-merlin.php:1572
msgid "Your license key has reached its activation limit."
msgstr ""

#: includes/merlin/class-merlin.php:1666
msgid "The child theme functions.php content was generated"
msgstr ""

#: includes/merlin/class-merlin.php:1697
msgid "The child theme style.css content was generated"
msgstr ""

#: includes/merlin/class-merlin.php:1731
msgid ""
"The child theme screenshot was copied to the child theme, with the following "
"result"
msgstr ""

#: includes/merlin/class-merlin.php:1733
msgid "The child theme screenshot was not generated, because of these results"
msgstr ""

#: includes/merlin/class-merlin.php:1762
msgid "Activating"
msgstr ""

#: includes/merlin/class-merlin.php:1778
msgid "Updating"
msgstr ""

#: includes/merlin/class-merlin.php:1794 includes/merlin/class-merlin.php:1810
#: includes/merlin/class-merlin.php:2036 includes/merlin/class-merlin.php:2049
#: includes/merlin/class-merlin.php:2062 includes/merlin/class-merlin.php:2075
#: includes/merlin/class-merlin.php:2088 includes/merlin/class-merlin.php:2101
#: includes/merlin/class-merlin.php:2114 includes/merlin/class-merlin.php:2157
msgid "Installing"
msgstr ""

#: includes/merlin/class-merlin.php:1802
msgid "A plugin with the following data will be processed"
msgstr ""

#: includes/merlin/class-merlin.php:1814
msgid "A plugin with the following data was processed"
msgstr ""

#: includes/merlin/class-merlin.php:1823 includes/merlin/class-merlin.php:2037
#: includes/merlin/class-merlin.php:2050 includes/merlin/class-merlin.php:2063
#: includes/merlin/class-merlin.php:2076 includes/merlin/class-merlin.php:2089
#: includes/merlin/class-merlin.php:2102 includes/merlin/class-merlin.php:2115
msgid "Success"
msgstr ""

#: includes/merlin/class-merlin.php:1846
msgid ""
"The content importer AJAX call failed to start, because of incorrect data"
msgstr ""

#: includes/merlin/class-merlin.php:1851
msgid "Invalid content!"
msgstr ""

#: includes/merlin/class-merlin.php:1862
msgid "The content import AJAX call will be executed with this import data"
msgstr ""

#: includes/merlin/class-merlin.php:1904
msgid "The content import AJAX call failed with this passed data"
msgstr ""

#: includes/merlin/class-merlin.php:1915
msgid "Error"
msgstr ""

#: includes/merlin/class-merlin.php:1929
msgid ""
"The content importer AJAX call for retrieving total content import items "
"failed to start, because of incorrect data."
msgstr ""

#: includes/merlin/class-merlin.php:1934
msgid "Invalid data!"
msgstr ""

#: includes/merlin/class-merlin.php:2034
msgid "Demo content data."
msgstr ""

#: includes/merlin/class-merlin.php:2035 includes/merlin/class-merlin.php:2048
#: includes/merlin/class-merlin.php:2061 includes/merlin/class-merlin.php:2074
#: includes/merlin/class-merlin.php:2087 includes/merlin/class-merlin.php:2100
#: includes/merlin/class-merlin.php:2113
msgid "Pending"
msgstr ""

#: includes/merlin/class-merlin.php:2046
msgid "Widgets"
msgstr ""

#: includes/merlin/class-merlin.php:2047
msgid "Sample widgets data."
msgstr ""

#: includes/merlin/class-merlin.php:2059
msgid "Revolution Slider"
msgstr ""

#: includes/merlin/class-merlin.php:2060
msgid "Sample Revolution sliders data."
msgstr ""

#: includes/merlin/class-merlin.php:2072
msgid "Options"
msgstr ""

#: includes/merlin/class-merlin.php:2073
msgid "Sample theme options data."
msgstr ""

#: includes/merlin/class-merlin.php:2085
msgid "Redux Options"
msgstr ""

#: includes/merlin/class-merlin.php:2086
msgid "Redux framework options."
msgstr ""

#: includes/merlin/class-merlin.php:2098
msgid "Option Tree Options"
msgstr ""

#: includes/merlin/class-merlin.php:2099
msgid "Option Tree framework options."
msgstr ""

#: includes/merlin/class-merlin.php:2111
msgid "After import setup"
msgstr ""

#: includes/merlin/class-merlin.php:2112
msgid "After import setup."
msgstr ""

#: includes/merlin/class-merlin.php:2141
msgid "The revolution slider import was executed"
msgstr ""

#: includes/merlin/class-merlin.php:2178
msgid "The home page was set"
msgstr ""

#: includes/merlin/class-merlin.php:2188
msgid "The blog page was set"
msgstr ""

#: includes/merlin/class-merlin.php:2203
msgid "The Hello world post status was set to draft"
msgstr ""

#: includes/merlin/class-merlin.php:2227
msgid ""
"This predefined demo import does not have the name parameter: "
"import_file_name"
msgstr ""

#: includes/merlin/includes/class-merlin-customizer-importer.php:30
msgid "The customizer import has finished successfully"
msgstr ""

#: includes/merlin/includes/class-merlin-customizer-importer.php:57
#, php-format
msgid "Error: The customizer import file is missing! File path: %s"
msgstr ""

#: includes/merlin/includes/class-merlin-customizer-importer.php:70
msgid ""
"Error: The customizer import file does not have any content in it. Please "
"make sure to use the correct customizer import file."
msgstr ""

#: includes/merlin/includes/class-merlin-customizer-importer.php:80
msgid ""
"Error: The customizer import file is not in a correct format. Please make "
"sure to use the correct customizer import file."
msgstr ""

#: includes/merlin/includes/class-merlin-customizer-importer.php:86
msgid ""
"Error: The customizer import file is not suitable for current theme. You can "
"only import customizer settings for the same theme or a child theme."
msgstr ""

#: includes/merlin/includes/class-merlin-downloader.php:49
msgid "The file was not able to save to disk, while trying to download it"
msgstr ""

#: includes/merlin/includes/class-merlin-downloader.php:66
msgid "Missing URL for downloading a file!"
msgstr ""

#: includes/merlin/includes/class-merlin-downloader.php:84
#, php-format
msgid ""
"An error occurred while fetching file from: %1$s%2$s%3$s!%4$sReason: %5$s - "
"%6$s."
msgstr ""

#: includes/merlin/includes/class-merlin-option-tree-importer.php:31
msgid "The Option Tree data was imported"
msgstr ""

#: includes/merlin/includes/class-merlin-redux-importer.php:32
msgid "The Redux Framework data was imported"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:72
msgid "Error: Widget import file could not be found."
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:83
msgid "Error: Widget import file does not have any content in it."
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:105
msgid ""
"Error: Widget import data could not be read. Please try a different file."
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:144
msgid "Sidebar does not exist in theme (moving widget to Inactive)"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:165
msgid "Site does not support widget"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:198
msgid "Widget already exists"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:256
msgid "Imported"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:260
msgid "Imported to Inactive"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:266
msgid "No Title"
msgstr ""

#: includes/merlin/includes/class-merlin-widget-importer.php:328
msgid "No results for widget import!"
msgstr ""

#: includes/merlin/merlin-config.php:38
msgid "Theme Setup"
msgstr ""

#: includes/merlin/merlin-config.php:41
#, php-format
msgid "%1$s%2$s Themes &lsaquo; Theme Setup: %3$s%4$s"
msgstr ""

#: includes/merlin/merlin-config.php:42
msgid "Return to the dashboard"
msgstr ""

#: includes/merlin/merlin-config.php:43
msgid "Disable this wizard"
msgstr ""

#: includes/merlin/merlin-config.php:45
msgid "Skip"
msgstr ""

#: includes/merlin/merlin-config.php:46
msgid "Next"
msgstr ""

#: includes/merlin/merlin-config.php:47
msgid "Start"
msgstr ""

#: includes/merlin/merlin-config.php:48
msgid "Cancel"
msgstr ""

#: includes/merlin/merlin-config.php:52
msgid "Import"
msgstr ""

#: includes/merlin/merlin-config.php:54
msgid "Later"
msgstr ""

#: includes/merlin/merlin-config.php:57
#, php-format
msgid "Activate %s"
msgstr ""

#: includes/merlin/merlin-config.php:59
#, php-format
msgid "%s is Activated"
msgstr ""

#: includes/merlin/merlin-config.php:61
msgid "Enter your license key to enable remote updates and theme support."
msgstr ""

#: includes/merlin/merlin-config.php:62
msgid "License key"
msgstr ""

#: includes/merlin/merlin-config.php:63
msgid "The theme is already registered, so you can go to the next step!"
msgstr ""

#: includes/merlin/merlin-config.php:64
msgid "Your theme is activated! Remote updates and theme support are enabled."
msgstr ""

#: includes/merlin/merlin-config.php:65
msgid "Need help?"
msgstr ""

#: includes/merlin/merlin-config.php:68
#, php-format
msgid "Welcome to %s"
msgstr ""

#: includes/merlin/merlin-config.php:69
msgid "Hi. Welcome back"
msgstr ""

#: includes/merlin/merlin-config.php:70
msgid ""
"This wizard will set up your theme, install plugins, and import content. It "
"is optional & should take only a few minutes."
msgstr ""

#: includes/merlin/merlin-config.php:71
msgid ""
"You may have already run this theme setup wizard. If you would like to "
"proceed anyway, click on the \"Start\" button below."
msgstr ""

#: includes/merlin/merlin-config.php:73
msgid "Install Child Theme"
msgstr ""

#: includes/merlin/merlin-config.php:74
msgid "You're good to go!"
msgstr ""

#: includes/merlin/merlin-config.php:75
msgid ""
"Let's build & activate a child theme so you may easily make theme changes."
msgstr ""

#: includes/merlin/merlin-config.php:76
msgid ""
"Your child theme has already been installed and is now activated, if it "
"wasn't already."
msgstr ""

#: includes/merlin/merlin-config.php:77
msgid "Learn about child themes"
msgstr ""

#: includes/merlin/merlin-config.php:78
msgid ""
"Awesome. Your child theme has already been installed and is now activated."
msgstr ""

#: includes/merlin/merlin-config.php:79
msgid "Awesome. Your child theme has been created and is now activated."
msgstr ""

#: includes/merlin/merlin-config.php:82
msgid "You're up to speed!"
msgstr ""

#: includes/merlin/merlin-config.php:83
msgid ""
"Let's install some essential WordPress plugins to get your site up to speed."
msgstr ""

#: includes/merlin/merlin-config.php:84
msgid ""
"The required WordPress plugins are all installed and up to date. Press "
"\"Next\" to continue the setup wizard."
msgstr ""

#: includes/merlin/merlin-config.php:85 includes/merlin/merlin-config.php:89
msgid "Advanced"
msgstr ""

#: includes/merlin/merlin-config.php:87
msgid "Import Content"
msgstr ""

#: includes/merlin/merlin-config.php:88
msgid ""
"Let's import content to your website, to help you get familiar with the "
"theme."
msgstr ""

#: includes/merlin/merlin-config.php:91
msgid "All done. Have fun!"
msgstr ""

#: includes/merlin/merlin-config.php:94
#, php-format
msgid "Your theme has been all set up. Enjoy your new theme by %s."
msgstr ""

#: includes/merlin/merlin-config.php:95
msgid "Extras"
msgstr ""

#: includes/merlin/merlin-config.php:96
msgid "View your website"
msgstr ""

#: includes/merlin/merlin-config.php:97
msgid "Explore WordPress"
msgstr ""

#: includes/merlin/merlin-config.php:98
msgid "Get Theme Support"
msgstr ""

#: includes/merlin/merlin-config.php:99
msgid "Start Customizing"
msgstr ""

#: includes/merlin/theme-register.php:9
msgid "KlbTheme"
msgstr ""

#: includes/merlin/theme-register.php:10 includes/merlin/theme-register.php:71
#: includes/merlin/theme-register.php:88
msgid "Register Theme"
msgstr ""

#: includes/merlin/theme-register.php:11
msgid "Theme Options"
msgstr ""

#: includes/merlin/theme-register.php:16
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: includes/merlin/theme-register.php:30
msgid "The theme registered"
msgstr ""

#: includes/merlin/theme-register.php:31
msgid ""
"Envato allows 1 license for 1 project located on 1 domain. It means that 1 "
"purchase key can be used only for 1 site. Each additional site will require "
"an additional purchase key."
msgstr ""

#: includes/merlin/theme-register.php:40
msgid "Documentation"
msgstr ""

#: includes/merlin/theme-register.php:47
msgid "Reset License"
msgstr ""

#: includes/merlin/theme-register.php:54
msgid "Support"
msgstr ""

#: includes/merlin/theme-register.php:61
msgid "Hire Us"
msgstr ""

#: includes/merlin/theme-register.php:72
msgid ""
"You're almost done. Just one more step. In order to gain full access to all "
"demos, \n"
"\t\tpremium plugins and support, please register your theme's purchase code."
msgstr ""

#: includes/merlin/theme-register.php:74
msgid "Your Envato Purchase Code"
msgstr ""

#: includes/merlin/theme-register.php:78
msgid "Where to find the code?"
msgstr ""

#: includes/merlin/theme-register.php:83
msgid ""
"I confirm that, according to the Envato License Terms, I am licensed to use "
"the purchase code for a single project. Using it on multiple installations "
"is a copyright violation."
msgstr ""

#: includes/merlin/theme-register.php:84
msgid "License details."
msgstr ""

#: includes/merlin/theme-register.php:98
msgid "WordPress"
msgstr ""

#: includes/merlin/theme-register.php:102
msgid "Theme Name"
msgstr ""

#: includes/merlin/theme-register.php:111
msgid "Theme Version"
msgstr ""

#: includes/merlin/theme-register.php:120
msgid "WP Version"
msgstr ""

#: includes/merlin/theme-register.php:129
msgid "WP Multisite"
msgstr ""

#: includes/merlin/theme-register.php:132
#: includes/merlin/theme-register.php:306
msgid "Yes"
msgstr ""

#: includes/merlin/theme-register.php:132
#: includes/merlin/theme-register.php:303
msgid "No"
msgstr ""

#: includes/merlin/theme-register.php:138
msgid "WP Debug Mode"
msgstr ""

#: includes/merlin/theme-register.php:141
msgid "Enabled"
msgstr ""

#: includes/merlin/theme-register.php:141
msgid "Disabled"
msgstr ""

#: includes/merlin/theme-register.php:145
msgid "Server"
msgstr ""

#: includes/merlin/theme-register.php:150
msgid "PHP Version"
msgstr ""

#: includes/merlin/theme-register.php:159
msgid "Minimum required PHP version 7.2"
msgstr ""

#: includes/merlin/theme-register.php:173
msgid "PHP Post Max Size"
msgstr ""

#: includes/merlin/theme-register.php:183
#: includes/merlin/theme-register.php:271
msgid "Minimum required value 64M."
msgstr ""

#: includes/merlin/theme-register.php:195
msgid "PHP Time Limit"
msgstr ""

#: includes/merlin/theme-register.php:205
msgid "Minimum required value 180."
msgstr ""

#: includes/merlin/theme-register.php:217
msgid "PHP Max Input Vars"
msgstr ""

#: includes/merlin/theme-register.php:227
msgid "Minimum required value 10000."
msgstr ""

#: includes/merlin/theme-register.php:239
msgid "PHP Memory Limit"
msgstr ""

#: includes/merlin/theme-register.php:249
msgid "Minimum required value 128M."
msgstr ""

#: includes/merlin/theme-register.php:261
msgid "PHP Upload Max Size"
msgstr ""

#: includes/merlin/theme-register.php:282
msgid "PHP Function \"file_get_content\""
msgstr ""

#: includes/merlin/theme-register.php:287
msgid "Off"
msgstr ""

#: includes/merlin/theme-register.php:290
msgid "On"
msgstr ""

#: includes/merlin/theme-register.php:298
msgid "DOMDocument"
msgstr ""

#: includes/merlin/theme-register.php:313
msgid "Active Plugins"
msgstr ""

#: includes/merlin/theme-register.php:364
msgid "The theme registered succesfully"
msgstr ""

#: includes/merlin/theme-register.php:392
#, php-format
msgid ""
"<a style=\"color: #ff0000;\" href=\"%s\">Register theme to unblock it</a>"
msgstr ""

#: includes/merlin/theme-register.php:409
#, php-format
msgid "Enter your Envato Purchase Code to receive Theme and plugin updates %s"
msgstr ""

#: includes/merlin/theme-register.php:410
msgid "Enter Purchase Code"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/Importer.php:55
msgid ""
"The XMLReader class is missing! Please install the XMLReader PHP extension "
"on your server"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/Importer.php:68
msgid "Could not open the XML file for parsing!"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/Importer.php:248
msgid "Content import start error: "
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/Importer.php:280
#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:173
#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:291
#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:375
#, php-format
msgid ""
"This WXR file (version %s) is newer than the importer (version %s) and may "
"not be supported. Please consider updating."
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/Importer.php:504
msgid "New AJAX call!"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:135
msgid "Could not open the file for parsing"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:525
msgid "The file does not exist, please try again."
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:588
msgid "Invalid author mapping"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:689
msgid "Cannot import auto-draft posts"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:781
#, php-format
msgid "Failed to import \"%s\": Invalid post type %s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:791
#, php-format
msgid "%s \"%s\" already exists."
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:871
#, php-format
msgid "Skipping attachment \"%s\", fetching attachments disabled"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:885
#, php-format
msgid "Failed to import \"%s\" (%s)"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:917
#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1835
#, php-format
msgid "Imported \"%s\" (%s)"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:922
#, php-format
msgid "Post %d remapped to %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:962
#, php-format
msgid "Failed to import term: %s - %s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1107
msgid "Invalid file type"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1606
#, php-format
msgid "Failed to import user \"%s\""
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1627
#, php-format
msgid "Imported user \"%s\""
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1631
#, php-format
msgid "User %d remapped to %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1796
#, php-format
msgid "Failed to import %s %s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1840
#, php-format
msgid "Term %d remapped to %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1899
#, php-format
msgid "Failed to add metakey: %s, metavalue: %s to term_id: %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1908
#, php-format
msgid "Meta for term_id %d : %s => %s ; successfully added!"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1960
#, php-format
msgid "Remote server returned %1$d %2$s for %3$s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1982
msgid "Zero size file downloaded"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:1988
#, php-format
msgid "Remote file is too large, limit is %s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2013
#, php-format
msgid "Running post-processing for post %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2026
#, php-format
msgid "Could not find the post parent for \"%s\" (post #%d)"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2031
#, php-format
msgid "Post %d was imported with parent %d, but could not be found"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2045
#, php-format
msgid "Could not find the author for \"%s\" (post #%d)"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2050
#, php-format
msgid "Post %d was imported with author \"%s\", but could not be found"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2076
#, php-format
msgid "Post %d was marked for post-processing, but none was required."
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2087
#, php-format
msgid "Could not update \"%s\" (post #%d) with mapped data"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2132
#, php-format
msgid "Could not find the menu object for \"%s\" (post #%d)"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2137
#, php-format
msgid ""
"Post %d was imported with object \"%d\" of type \"%s\", but could not be "
"found"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2159
#, php-format
msgid "Could not find the comment parent for comment #%d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2163
#, php-format
msgid "Comment %d was imported with parent %d, but could not be found"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2177
#, php-format
msgid "Could not find the author for comment #%d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2181
#, php-format
msgid "Comment %d was imported with author %d, but could not be found"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2198
#, php-format
msgid "Could not update comment #%d with mapped data"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2229
#, php-format
msgid "Faulty term_id provided in terms-to-be-remapped array %s"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2239
#, php-format
msgid "No taxonomy provided in terms-to-be-remapped array for term #%d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2249
#, php-format
msgid "No parent_slug identified in remapping-array for term: %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2257
#, php-format
msgid "The term(%d)\"s parent_slug (%s) is not found in the remapping-array."
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2271
#, php-format
msgid "No data returned by get_term_by for term_id #%d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2290
#, php-format
msgid "Could not update \"%s\" (term #%d) with mapped data"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2300
#, php-format
msgid "Term %d was successfully updated with parent %d"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2335
msgid "Starting remapping of featured images"
msgstr ""

#: includes/merlin/vendor/proteusthemes/wp-content-importer-v2/src/WXRImporter.php:2344
#, php-format
msgid "Remapping featured image ID %d to new ID %d for post ID %d"
msgstr ""

#: includes/metaboxes.php:26
msgid "Product Specification"
msgstr ""

#: includes/metaboxes.php:44
msgid "Blog Post Image Slides"
msgstr ""

#: includes/metaboxes.php:50
msgid "Blog Post Slider Images"
msgstr ""

#: includes/metaboxes.php:51
msgid ""
"Upload unlimited images for a slideshow - or only one to display a single "
"image."
msgstr ""

#: includes/metaboxes.php:73
msgid "Enter your Audio URL(Oembed) or Embed Code."
msgstr ""

#: includes/metaboxes.php:89
msgid "Blog Video Settings"
msgstr ""

#: includes/metaboxes.php:95
msgid "Video Type"
msgstr ""

#: includes/metaboxes.php:99
msgid "Youtube"
msgstr ""

#: includes/metaboxes.php:100
msgid "Vimeo"
msgstr ""

#: includes/metaboxes.php:101
msgid "Own Embed Code"
msgstr ""

#: includes/metaboxes.php:108
msgid "Embed Code<br />(Audio Embed Code is possible, too)"
msgstr ""

#: includes/metaboxes.php:110
msgid ""
"Just paste the ID of the video (E.g. http://www.youtube.com/watch?"
"v=<strong>GUEZCxBcM78</strong>) you want to show, or insert own Embed Code. "
"<br />This will show the Video <strong>INSTEAD</strong> of the Image Slider."
"<br /><strong>Of course you can also insert your Audio Embedd Code!</strong>"
msgstr ""

#: includes/woocommerce.php:204 includes/woocommerce.php:215
msgid "Store:"
msgstr ""

#: includes/woocommerce.php:279
msgid "Quick Shop"
msgstr ""

#: includes/woocommerce.php:340
#: includes/woocommerce/product-type/product-type-header.php:32
#: includes/woocommerce/product-type/product-type-list.php:44
#: includes/woocommerce/product-type/product-type-list2.php:30
#: includes/woocommerce/product-type/product-type-list3.php:31
#: includes/woocommerce/product-type/product-type10.php:30
#: includes/woocommerce/product-type/product-type11.php:30
#: includes/woocommerce/product-type/product-type12.php:31
#: includes/woocommerce/product-type/product-type2.php:29
#: includes/woocommerce/product-type/product-type3.php:30
#: includes/woocommerce/product-type/product-type4.php:30
#: includes/woocommerce/product-type/product-type5.php:30
#: includes/woocommerce/product-type/product-type6.php:30
#: includes/woocommerce/product-type/product-type7.php:30
#: includes/woocommerce/product-type/product-type8.php:30
#: includes/woocommerce/product-type/product-type9.php:30
#, php-format
msgid "Only %s left in stock"
msgstr ""

#: includes/woocommerce.php:391
msgid "This product has been added to"
msgstr ""

#: includes/woocommerce.php:391
#, php-format
msgid "%d  people's"
msgstr ""

#: includes/woocommerce.php:391
msgid "carts."
msgstr ""

#: includes/woocommerce.php:493
#, php-format
msgid "%d item"
msgid_plural "%d items"
msgstr[0] ""
msgstr[1] ""

#: includes/woocommerce.php:508
msgid "Search"
msgstr ""

#: includes/woocommerce.php:569
msgid "All"
msgstr ""

#: includes/woocommerce.php:787
msgid "In Stock"
msgstr ""

#: includes/woocommerce.php:791
msgid "Out of stock"
msgstr ""

#: includes/woocommerce.php:835
msgid "Specification"
msgstr ""

#: includes/woocommerce/add-to-cart.php:155
msgid "View cart"
msgstr ""

#: includes/woocommerce/add-to-cart.php:158
msgid "Cart Updated"
msgstr ""

#: includes/woocommerce/product-type/product-type-header.php:72
#: includes/woocommerce/product-type/product-type-header.php:88
#: includes/woocommerce/product-type/product-type-list-view.php:76
#: includes/woocommerce/product-type/product-type-list3.php:70
#: includes/woocommerce/product-type/product-type1.php:87
#: includes/woocommerce/product-type/product-type1.php:101
#: includes/woocommerce/product-type/product-type12.php:81
#, php-format
msgid "%d Review"
msgid_plural "%d Reviews"
msgstr[0] ""
msgstr[1] ""

#: includes/woocommerce/product-type/product-type-list-view.php:32
#: includes/woocommerce/product-type/product-type1.php:34
#: includes/woocommerce/product-type/product-type13.php:34
#, php-format
msgid "Only %s unit left"
msgstr ""

#: includes/woocommerce/product-type/product-type-list.php:96
msgid "Remains until the end of the offer"
msgstr ""

#: page.php:23 page.php:34 page.php:45 page.php:53 page.php:66 page.php:83
#: page.php:96 post-format/content-audio.php:26
#: post-format/content-gallery.php:36 post-format/content-video.php:36
#: post-format/content.php:35 post-format/single-audio.php:32
#: post-format/single-gallery.php:42 post-format/single-video.php:42
#: post-format/single.php:38
msgid "Pages:"
msgstr ""

#: post-format/content-audio.php:21 post-format/content-gallery.php:31
#: post-format/content-video.php:31 post-format/content.php:30
#: post-format/single-audio.php:18 post-format/single-gallery.php:18
#: post-format/single-video.php:18 post-format/single.php:18
msgid "Featured"
msgstr ""

#: search.php:24 search.php:54 search.php:76 search.php:106
#, php-format
msgid "Search Results for: %s"
msgstr ""

#: searchform.php:12
msgid "Search..."
msgstr ""

#: woocommerce/cart/cart-empty.php:71
msgid "Return to shop"
msgstr ""

#: woocommerce/cart/cart.php:31 woocommerce/cart/cart.php:72
msgid "Product"
msgstr ""

#: woocommerce/cart/cart.php:32 woocommerce/cart/cart.php:97
msgid "Price"
msgstr ""

#: woocommerce/cart/cart.php:33 woocommerce/cart/cart.php:103
#: woocommerce/global/quantity-input.php:24
msgid "Quantity"
msgstr ""

#: woocommerce/cart/cart.php:34 woocommerce/cart/cart.php:125
msgid "Subtotal"
msgstr ""

#: woocommerce/cart/cart.php:92
msgid "Available on backorder"
msgstr ""

#: woocommerce/cart/cart.php:139 woocommerce/cart/mini-cart.php:73
#, php-format
msgid "Remove %s from cart"
msgstr ""

#: woocommerce/cart/cart.php:160
msgid "Coupon:"
msgstr ""

#: woocommerce/cart/cart.php:161
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:162
msgid "Apply coupon"
msgstr ""

#: woocommerce/cart/cart.php:167
msgid "Update cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:138
msgid "No products in the cart."
msgstr ""

#: woocommerce/checkout/form-checkout.php:26
msgid "You must be logged in to checkout."
msgstr ""

#: woocommerce/checkout/form-checkout.php:55
msgid "Your order"
msgstr ""

#: woocommerce/content-widget-price-filter.php:28
#: woocommerce/content-widget-price-filter.php:29
msgid "Min price"
msgstr ""

#: woocommerce/content-widget-price-filter.php:31
msgid "-"
msgstr ""

#: woocommerce/content-widget-price-filter.php:33
#: woocommerce/content-widget-price-filter.php:34
msgid "Max price"
msgstr ""

#: woocommerce/content-widget-price-filter.php:40
msgid "Filter"
msgstr ""

#: woocommerce/content-widget-price-filter.php:43
msgid "Price:"
msgstr ""

#: woocommerce/global/quantity-input.php:24
#, php-format
msgid "%s quantity"
msgstr ""

#: woocommerce/global/quantity-input.php:50
msgid "Product quantity"
msgstr ""

#: woocommerce/loop/no-products-found.php:33
msgid "No products found!"
msgstr ""

#: woocommerce/loop/no-products-found.php:35
msgid "No products were found matching your selection."
msgstr ""

#: woocommerce/loop/orderby.php:30
msgid "Sort:"
msgstr ""

#: woocommerce/loop/orderby.php:32
msgid "Shop order"
msgstr ""

#: woocommerce/myaccount/form-login.php:33
#: woocommerce/myaccount/form-login.php:136
msgid "Login"
msgstr ""

#: woocommerce/myaccount/form-login.php:36
#: woocommerce/myaccount/form-login.php:114
#: woocommerce/myaccount/form-login.php:174
#: woocommerce/myaccount/form-login.php:211
msgid "Register"
msgstr ""

#: woocommerce/myaccount/form-login.php:41
msgid "If you have an account, sign in with your username or email address."
msgstr ""

#: woocommerce/myaccount/form-login.php:78
msgid ""
"There are many advantages to creating an account: the payment process is "
"faster, shipment tracking is possible and much more."
msgstr ""

#: woocommerce/myaccount/form-login.php:86
#: woocommerce/myaccount/form-login.php:183
msgid "Username"
msgstr ""

#: woocommerce/myaccount/form-login.php:93
#: woocommerce/myaccount/form-login.php:190
msgid "Email address"
msgstr ""

#: woocommerce/myaccount/form-login.php:106
#: woocommerce/myaccount/form-login.php:203
msgid "A link to set a new password will be sent to your email address."
msgstr ""

#: woocommerce/myaccount/navigation.php:38
msgid "Welcome back,"
msgstr ""

#: woocommerce/single-product-reviews.php:34
#, php-format
msgid "%1$s review for %2$s"
msgid_plural "%1$s reviews for %2$s"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product-reviews.php:37
msgid "Reviews"
msgstr ""

#: woocommerce/single-product-reviews.php:66
msgid "There are no reviews yet."
msgstr ""

#: woocommerce/single-product-reviews.php:77
msgid "Add a review"
msgstr ""

#: woocommerce/single-product-reviews.php:77
#, php-format
msgid "Be the first to review &ldquo;%s&rdquo;"
msgstr ""

#: woocommerce/single-product-reviews.php:79
#, php-format
msgid "Leave a Reply to %s"
msgstr ""

#: woocommerce/single-product-reviews.php:83
msgid "Submit"
msgstr ""

#: woocommerce/single-product-reviews.php:91
msgid "Name"
msgstr ""

#: woocommerce/single-product-reviews.php:97
msgid "Email"
msgstr ""

#: woocommerce/single-product-reviews.php:122
#, php-format
msgid "You must be %1$slogged in%2$s to post a review."
msgstr ""

#: woocommerce/single-product-reviews.php:126
msgid "Your rating"
msgstr ""

#: woocommerce/single-product-reviews.php:127
msgid "Rate&hellip;"
msgstr ""

#: woocommerce/single-product-reviews.php:128
msgid "Perfect"
msgstr ""

#: woocommerce/single-product-reviews.php:129
msgid "Good"
msgstr ""

#: woocommerce/single-product-reviews.php:130
msgid "Average"
msgstr ""

#: woocommerce/single-product-reviews.php:131
msgid "Not that bad"
msgstr ""

#: woocommerce/single-product-reviews.php:132
msgid "Very poor"
msgstr ""

#: woocommerce/single-product-reviews.php:136
msgid "Your review"
msgstr ""

#: woocommerce/single-product-reviews.php:143
msgid ""
"Only logged in customers who have purchased this product may leave a review."
msgstr ""

#: woocommerce/single-product/meta.php:31
msgid "SKU:"
msgstr ""

#: woocommerce/single-product/meta.php:32
msgid "N/A"
msgstr ""

#: woocommerce/single-product/meta.php:37
msgid "<span>Category:</span>"
msgid_plural "<span>Categories:</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:39
msgid "<span>Tag:</span>"
msgid_plural "<span>Tags:</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/product-image.php:50
msgid "Awaiting product image"
msgstr ""

#: woocommerce/single-product/rating.php:40
#, php-format
msgid "%s review"
msgid_plural "%s reviews"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/related.php:27
msgid "Related products"
msgstr ""

#. Theme Name of the plugin/theme
msgid "Blonwe"
msgstr ""

#. Theme URI of the plugin/theme
msgid "http://themeforest.net/user/klbtheme/portfolio"
msgstr ""

#. Description of the plugin/theme
msgid ""
"This WordPress theme is best for use in such business areas as electronics "
"store, fashion store, furniture store, grocery store, auto parts store and "
"responsive ecommerce."
msgstr ""

#. Author of the plugin/theme
msgid "KlbTheme (Sinan ISIK)"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://themeforest.net/user/KlbTheme"
msgstr ""
