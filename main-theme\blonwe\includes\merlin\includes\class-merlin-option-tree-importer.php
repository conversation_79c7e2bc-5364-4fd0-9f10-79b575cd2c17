<?php
/**
 * Class for the Option tree importer.
 *
 * @see https://wordpress.org/plugins/option-tree/
 *
 * @package Merlin WP
 */
class Merlin_Option_Tree_Importer {
	/**
	 * Import Option Tree data from a text file, generated by the Option Tree plugin.
	 *
	 *
	 * @return boolean
	 */
	public static function import() {
		// Option Tree plugin is not active!
		if ( ! class_exists( 'OT_Loader' ) ) {
			return false;
		}
		global $wp_filesystem;
	    require_once ( ABSPATH . '/wp-admin/includes/file.php' );
	    WP_Filesystem();
		$local_file = get_parent_theme_file_path( '/includes/merlin/demo-data/optiontree.txt' );
		$decoded = '';
		$decoded = base64_decode( $wp_filesystem->get_contents( $local_file ) );
		$data = maybe_unserialize( $decoded );
		if ( $wp_filesystem->exists( $local_file ) ) {
			update_option( 'option_tree', $data );
		}
		Merlin_Logger::get_instance()->debug( esc_html__( 'The Option Tree data was imported' , 'merlin-wp'), $data );
		return true;
	}
}