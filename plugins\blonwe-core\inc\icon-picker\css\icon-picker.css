.klb-iconsholder-wrapper{
    display: none;
}

.klb-iconsholder {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
	justify-content: center;
    flex-wrap: wrap;
    max-height: 300px;
    overflow-y: scroll;
    border: 1px solid #DEE2E6;
    box-shadow: 1px 2px 4px rgb(33 37 41 / 5%);
    margin-top: 10px;
    margin-bottom: 5px;
}

.klb-iconsholder::-webkit-scrollbar {
	width: 3px;
}

.klb-iconsholder::-webkit-scrollbar-track {
	background-color: #f6f4f0;
	border-radius: 9px;
}
.klb-iconsholder::-webkit-scrollbar-thumb {
	background-color: #c1c1c1;
	border-radius: 9px;
}

.klb-iconbox {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 16.6666666%;
    flex: 1 0 16.6666666%;
    text-align: center;
    cursor: pointer;
}

.klb-iconbox p.icon {
    background: #fcfcfc;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 3px;
    margin: 5px;
    width: 28px;
    height: 28px;
    overflow: hidden;
    display: inline-block;
}

.klb-iconbox i {
    display: inline-block;
    margin-bottom: 10px;
    font-size: 17px;
}

.klb-iconsholder-wrapper input.iconsearch {
    width: 100%;
    margin: 5px;
    border: 1px solid #ccc;
}

select.iconcat {
    text-transform: capitalize;
    width: 100%;
    margin: 5px;
}