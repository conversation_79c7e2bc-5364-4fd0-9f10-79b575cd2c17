.footer-fix-nav {
    position: fixed;
    bottom: 0;
	left: 0;
	z-index: 99;
    right: 0;
    display: none;
    background: #fff;
    text-align: center;
}
.footer-fix-nav a i{
    font-size: 21px;
    padding: 20px 7px;
    display: block;
    color: #9b9b9b;
    font-weight: 500;
}
.footer-fix-nav .col {
    margin: 0px;
    padding: 0px;
    border-right: 1px solid #edf1f4;
    height: 61px;
}
.footer-fix-nav .col:last-child {
    border: none;
}
.footer-fix-nav .col.active a:hover, .footer-fix-nav .col.active a:focus{
	background: #000000 !important;
}
.footer-fix-nav .col.active a {
    color: #fff;
    font-size: 42px;
    padding: 18px;
    border-radius: 0px;
    background: #51aa1b;

}

@media (max-width: 575.98px) { 
    .footer-fix-nav {
        display: block;
    }
	
	li.list-inline-item.cart-btn {
		display: none;
	}
}

@media (min-width: 576px) and (max-width: 767.98px) { 
    .footer-fix-nav {
        display: block;
    }
	
	li.list-inline-item.cart-btn {
		display: none;
	}
}

@media (min-width: 768px) and (max-width: 991.98px) { 
    .footer-fix-nav {
        display: block;
    }
	
	li.list-inline-item.cart-btn {
		display: none;
	}
}

.footer-fix-nav span.count {
    background: #ff253a;
    background: -moz-linear-gradient(-45deg, #ff253a 0%, #ff8453 100%);
    background: -webkit-linear-gradient(-45deg, #ff253a 0%,#ff8453 100%);
    background: linear-gradient(135deg, #ff253a 0%,#ff8453 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff253a', endColorstr='#ff8453',GradientType=1 );
    border-radius: 50px;
    font-size: 10px;
    font-weight: 500;
    height: 18px;
    right: 21px;
    line-height: 17px;
    min-width: 18px;
    position: absolute;
    top: 17px;
    color: #fff;
}

.mobile-filter {
    background: #fff none repeat scroll 0 0;
    overflow: auto;
    position: fixed;
    left: -400px;
    top: 0;
    width: 400px;
	z-index: 99;
	  -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
	height: 100vh;
}
.toggled .mobile-filter {
	left: 0px;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	-o-transition: all 0.5s ease;
	transition: all 0.5s ease;
    z-index: 9999;
}
.mobile-filter-body {
    background: #eff7fa none repeat scroll 0 0;
    height: 66vh;
    overflow: inherit;
    padding: 18px 20px;
}
.mobile-filter-header {
    background: #1c2224 none repeat scroll 0 0;
    color: #fff;
    padding: 18px 20px;
}
.mobile-filter-header h5 {
    color: #fff;
    font-size: 16px;
    line-height: 24px;
    margin: 0;
}
.mobile-filter-header .float-right {
    background: #fff none repeat scroll 0 0;
    border-radius: 24px;
    color: #000;
    height: 26px;
    line-height: 25px;
    text-align: center;
    width: 26px;
}

@media (max-width: 575.98px) {
	.mobile-filter {
		width: 320px;
	}
}

.mobile-filter-header .float-right {
    float: right;
}

.mobile-filter .klb-sidebar.sidebar {
    background-color: #ffffff;
    padding: 20px 10px;
    margin-top: 0;
}