!function(r){"use strict";var o,i={cache:{},init:function(){this.bindEvents()},bindEvents:function(){var e=this;e.addItem(),e.removeItem(),e.tabbedNav(),r(document).on("click",".envato-card a.thickbox",function(){return tb_click.call(this),r("#TB_title").css({"background-color":"#23282d",color:"#cfcfcf"}),!1})},addItem:function(){r(document).on("click",".add-envato-market-item",function(e){var t="envato-market-dialog-form";e.preventDefault(),0===r("#"+t).length&&r("body").append(wp.template(t)),o=r("#"+t).dialog({autoOpen:!0,modal:!0,width:350,buttons:{Save:{text:_envatoMarket.i18n.save,click:function(){var e,t=r(this);t.on("submit",function(e){e.preventDefault()}),e=t.find('input[name="token"]').val(),t=t.find('input[name="id"]').val(),(e=wp.ajax.post(_envatoMarket.action+"_add_item",{nonce:_envatoMarket.nonce,token:e,id:t})).done(function(e){var t=wp.template("envato-market-item"),a=wp.template("envato-market-card"),n=wp.template("envato-market-auth-check-button");r(".nav-tab-wrapper").find('[data-id="'+e.type+'"]').removeClass("hidden"),e.item.type=e.type,r("#"+e.type+"s").append(a(e.item)).removeClass("hidden"),r("#envato-market-items").append(t({name:e.name,token:e.token,id:e.id,key:e.key,type:e.type,authorized:e.authorized})),0===r(".auth-check-button").length&&r("p.submit").append(n),o.dialog("close"),i.addReadmore()}),e.fail(function(e){var t=wp.template("envato-market-dialog-error"),e={message:e.message||_envatoMarket.i18n.error};o.find(".notice").remove(),o.find("form").prepend(t(e)),o.find(".notice").fadeIn("fast")})}},Cancel:{text:_envatoMarket.i18n.cancel,click:function(){o.dialog("close")}}},close:function(){o.find(".notice").remove(),o.find("form")[0].reset()}})})},removeItem:function(){r(document).on("click","#envato-market-items .item-delete",function(e){const n=this;var t="envato-market-dialog-remove";e.preventDefault(),0===r("#"+t).length&&r("body").append(wp.template(t)),o=r("#"+t).dialog({autoOpen:!0,modal:!0,width:350,buttons:{Save:{text:_envatoMarket.i18n.remove,click:function(){var e=r(this);let a;e.on("submit",function(e){e.preventDefault()}),a=r(n).parents("li").data("id"),(e=wp.ajax.post(_envatoMarket.action+"_remove_item",{nonce:_envatoMarket.nonce,id:a})).done(function(){var e=r('.col[data-id="'+a+'"]'),t=e.find(".envato-card").hasClass("theme")?"theme":"plugin";e.remove(),0===r("#"+t+"s").find(".col").length&&(r(".nav-tab-wrapper").find('[data-id="'+t+'"]').addClass("hidden"),r("#"+t+"s").addClass("hidden")),r(n).parents("li").remove(),r("#envato-market-items li").each(function(e){r(this).find("input").each(function(){r(this).attr("name",r(this).attr("name").replace(/\[\d\]/g,"["+e+"]"))})}),0!==r(".auth-check-button").length&&0===r("#envato-market-items li").length&&r("p.submit .auth-check-button").remove(),o.dialog("close")}),e.fail(function(e){var t=wp.template("envato-market-dialog-error"),e={message:e.message||_envatoMarket.i18n.error};o.find(".notice").remove(),o.find("form").prepend(t(e)),o.find(".notice").fadeIn("fast")})}},Cancel:{text:_envatoMarket.i18n.cancel,click:function(){o.dialog("close")}}}})})},tabbedNav:function(){const e=this,t=r(".about-wrap");r("div.panel",t).hide();var a=e.getParameterByName("tab"),n=window.location.hash.substr(1);r(document,t).on("click",".nav-tab-wrapper a",function(){return r(".nav-tab-wrapper a",t).removeClass("nav-tab-active"),r("div.panel",t).hide(),r(this).addClass("nav-tab-active"),r("div"+r(this).attr("href"),t).show(),e.maybeLoadhealthcheck(),!1}),a?r('.nav-tab-wrapper a[href="#'+a+'"]',t).click():n?r('.nav-tab-wrapper a[href="#'+n+'"]',t).click():r("div.panel:not(.hidden)",t).first().show()},getParameterByName:function(e){return e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),null===(e=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(location.search))?"":decodeURIComponent(e[1].replace(/\+/g," "))},maybeLoadhealthcheck:function(){const c=r(".envato-market-healthcheck");var e;c.is(":visible")&&(c.text("Loading..."),(e=wp.ajax.post(_envatoMarket.action+"_healthcheck",{nonce:_envatoMarket.nonce})).done(function(t){if(t&&t.limits){var a=r("<ul></ul>"),n=Object.keys(t.limits);for(let e=0;e<n.length;e++){var o=r("<li></li>"),i=t.limits[n[e]];o.addClass(i.ok?"healthcheck-ok":"healthcheck-error"),o.attr("data-limit",n[e]),o.append('<span class="healthcheck-item-title">'+i.title+"</span>"),o.append('<span class="healthcheck-item-message">'+i.message+"</span>"),a.append(o)}c.html(a)}else window.console.log(t),c.text("Health check failed to load. Please check console for errors.")}),e.fail(function(e){window.console.log(e),c.text("Health check failed to load. Please check console for errors.")}))}};r(window).on("load",function(){i.init()})}(jQuery);