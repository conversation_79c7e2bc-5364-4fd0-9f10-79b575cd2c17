<?php
/**
 * Import Helper - Optimize server settings for demo import
 * Place this file in the theme root and include it before import
 */

// Increase memory limit
@ini_set('memory_limit', '512M');

// Remove execution time limit
@ini_set('max_execution_time', 0);
@set_time_limit(0);

// Increase input variables limit
@ini_set('max_input_vars', '10000');

// Increase post max size
@ini_set('post_max_size', '64M');

// Increase upload max filesize
@ini_set('upload_max_filesize', '64M');

// Disable WordPress automatic updates during import
add_filter('automatic_updater_disabled', '__return_true');

// Increase WordPress memory limit
if (!defined('WP_MEMORY_LIMIT')) {
    define('WP_MEMORY_LIMIT', '512M');
}

// Disable object cache during import
if (!defined('WP_CACHE')) {
    define('WP_CACHE', false);
}

// Function to check server requirements
function blonwe_check_import_requirements() {
    $requirements = array();
    
    // Check memory limit
    $memory_limit = ini_get('memory_limit');
    $memory_limit_bytes = wp_convert_hr_to_bytes($memory_limit);
    $required_memory = 512 * 1024 * 1024; // 512MB
    
    $requirements['memory'] = array(
        'current' => $memory_limit,
        'required' => '512M',
        'status' => $memory_limit_bytes >= $required_memory ? 'ok' : 'warning'
    );
    
    // Check execution time
    $max_execution_time = ini_get('max_execution_time');
    $requirements['execution_time'] = array(
        'current' => $max_execution_time == 0 ? 'Unlimited' : $max_execution_time . 's',
        'required' => 'Unlimited or 300s+',
        'status' => ($max_execution_time == 0 || $max_execution_time >= 300) ? 'ok' : 'warning'
    );
    
    // Check max input vars
    $max_input_vars = ini_get('max_input_vars');
    $requirements['input_vars'] = array(
        'current' => $max_input_vars,
        'required' => '5000+',
        'status' => $max_input_vars >= 5000 ? 'ok' : 'warning'
    );
    
    return $requirements;
}

// Add admin notice for import requirements
function blonwe_import_requirements_notice() {
    if (isset($_GET['page']) && $_GET['page'] === 'merlin') {
        $requirements = blonwe_check_import_requirements();
        $has_warnings = false;
        
        foreach ($requirements as $req) {
            if ($req['status'] === 'warning') {
                $has_warnings = true;
                break;
            }
        }
        
        if ($has_warnings) {
            echo '<div class="notice notice-warning"><p>';
            echo '<strong>Import Requirements Check:</strong><br>';
            foreach ($requirements as $name => $req) {
                $status_icon = $req['status'] === 'ok' ? '✅' : '⚠️';
                echo sprintf('%s %s: %s (Required: %s)<br>', 
                    $status_icon, 
                    ucfirst(str_replace('_', ' ', $name)), 
                    $req['current'], 
                    $req['required']
                );
            }
            echo '</p></div>';
        }
    }
}
add_action('admin_notices', 'blonwe_import_requirements_notice');

// Optimize WordPress for import
function blonwe_optimize_for_import() {
    // Disable WordPress cron during import
    if (!defined('DISABLE_WP_CRON')) {
        define('DISABLE_WP_CRON', true);
    }
    
    // Increase WordPress timeout
    add_filter('http_request_timeout', function() { return 300; });
    
    // Disable image processing during import
    add_filter('intermediate_image_sizes_advanced', '__return_empty_array');
    
    // Disable term counting during import
    wp_defer_term_counting(true);
    
    // Disable comment counting during import
    wp_defer_comment_counting(true);
}

// Run optimization when accessing import page
if (isset($_GET['page']) && $_GET['page'] === 'merlin') {
    blonwe_optimize_for_import();
}

// Clean up after import
function blonwe_cleanup_after_import() {
    // Re-enable term counting
    wp_defer_term_counting(false);
    
    // Re-enable comment counting  
    wp_defer_comment_counting(false);
    
    // Clear any caches
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
    
    // Regenerate rewrite rules
    flush_rewrite_rules();
}

// Hook cleanup to import finished
add_action('import_end', 'blonwe_cleanup_after_import');
?>
