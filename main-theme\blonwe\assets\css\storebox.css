/* ----- storebox component ----- */
.klb-storebox {
  position: relative;
}
.klb-storebox.style-1 a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  color: currentColor;
  background-color: var(--color-gray50);
  border-radius: 0.25rem;
  padding: 1.125rem;
}
body[data-color=custom][data-theme=light] .klb-storebox.style-1 a {
  background-color: rgba(var(--color-rgb), 0.06);
}
body[data-color=default] .klb-storebox.style-1 a, body[data-color=custom][data-theme=dark] .klb-storebox.style-1 a {
  background-color: var(--color-gray50);
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-1 a {
    padding: 1.5rem;
  }
}
.klb-storebox.style-1 a .store-detail {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 0.9375rem;
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-1 a .store-detail {
    margin-bottom: 1.875rem;
  }
}
.klb-storebox.style-1 a .store-detail .store-avatar {
  position: relative;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 4rem;
          flex: 0 0 4rem;
  max-width: 4rem;
  border-radius: 50%;
  margin-right: 0.9375rem;
  overflow: hidden;
}
.klb-storebox.style-1 a .store-detail .store-info .store-name {
  font-size: 0.9375rem;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 0.1875rem;
}
.klb-storebox.style-1 a .store-detail .store-info .store-caption {
  font-size: 0.8125rem;
  font-weight: 500;
  opacity: 0.5;
}
.klb-storebox.style-1 a .store-detail .store-info .store-rating {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 0.75rem;
  line-height: 1;
  background-color: var(--color-background);
  padding: 0.25rem 0.5rem;
  border-radius: 1.25rem;
  margin-top: 0.4375rem;
}
.klb-storebox.style-1 a .store-detail .store-info .store-rating p {
  margin-bottom: 0;
}
.klb-storebox.style-1 a .store-detail .store-info .store-rating p strong {
  font-weight: 600;
}
.klb-storebox.style-1 a .store-detail .store-info .store-rating i {
  font-size: 0.875rem;
  color: var(--color-yellow600);
  margin-right: 0.25rem;
}
.klb-storebox.style-1 a .store-detail .store-info .store-rating i::before {
  margin: 0;
}
.klb-storebox.style-1 a .store-products {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-left: -0.125rem;
  margin-right: -0.125rem;
}
.klb-storebox.style-1 a .store-products .column {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 100%;
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-1 a .store-products .column:nth-child(1) {
    width: 66%;
  }
  .klb-storebox.style-1 a .store-products .column:nth-child(1) .product-item {
    height: 100%;
  }
  .klb-storebox.style-1 a .store-products .column:nth-child(2) {
    -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
            flex: 1 0 0%;
    width: auto;
  }
}
.klb-storebox.style-1 a .store-products .column:nth-child(2) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-left: -0.125rem;
  margin-right: -0.125rem;
}
@media screen and (max-width: 991.98px) {
  .klb-storebox.style-1 a .store-products .column:nth-child(2) {
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    margin-top: 0.25rem;
  }
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-1 a .store-products .column:nth-child(2) {
    gap: 0.25rem;
    margin-left: 0;
    margin-right: 0;
  }
}
.klb-storebox.style-1 a .store-products .column:nth-child(2) .child-column {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50%;
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-1 a .store-products .column:nth-child(2) .child-column {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
}
.klb-storebox.style-1 a .store-products .product-item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--color-background);
  padding: 0.9375rem;
  border-radius: 0.25rem;
  overflow: hidden;
}
.klb-storebox.style-1 a .store-products .product-item .product-price {
  position: absolute;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 1;
  left: 0;
  bottom: 0;
  color: #FFF;
  background-color: rgba(27, 31, 34, 0.6);
  padding: 0.1875rem 0.3125rem;
  opacity: 0;
}
.klb-storebox.style-1 a .store-products .product-item .product-price .price {
  font-size: 0.75rem;
  font-weight: 400;
}
body[data-color=custom][data-theme=light] .klb-storebox.style-1 a:hover {
  background-color: rgba(var(--color-rgb), 0.09);
}
body[data-color=default] .klb-storebox.style-1 a:hover, body[data-color=custom][data-theme=dark] .klb-storebox.style-1 a:hover {
  background-color: var(--color-gray100);
}
.klb-storebox.style-1 a:hover .product-item .product-price {
  opacity: 1;
}
.klb-storebox.style-2 a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  color: currentColor;
  padding: 1.125rem;
  border-radius: calc(theme(radius-base) / 2);
}
body[data-color=custom][data-theme=light] .klb-storebox.style-2 a {
  background-color: rgba(var(--color-rgb), 0.06);
}
body[data-color=default] .klb-storebox.style-2 a, body[data-color=custom][data-theme=dark] .klb-storebox.style-2 a {
  background-color: var(--color-gray50);
}
.klb-storebox.style-2 a .store-detail {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 0.9375rem;
}
@media screen and (min-width: 992px) {
  .klb-storebox.style-2 a .store-detail {
    margin-bottom: 1.875rem;
  }
}
.klb-storebox.style-2 a .store-detail .store-avatar {
  position: relative;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 4rem;
          flex: 0 0 4rem;
  max-width: 4rem;
  border-radius: 50%;
  margin-right: 0.9375rem;
  border: 1px solid;
  overflow: hidden;
}
body[data-color=custom][data-theme=light] .klb-storebox.style-2 a .store-detail .store-avatar {
  border-color: rgba(var(--color-rgb), 0.1);
}
body[data-color=default] .klb-storebox.style-2 a .store-detail .store-avatar, body[data-color=custom][data-theme=dark] .klb-storebox.style-2 a .store-detail .store-avatar {
  border-color: var(--color-gray200);
}
.klb-storebox.style-2 a .store-detail .store-info .store-name {
  font-size: 0.9375rem;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 0.1875rem;
}
.klb-storebox.style-2 a .store-detail .store-info .store-caption {
  font-size: 0.8125rem;
  font-weight: 500;
  opacity: 0.5;
}
.klb-storebox.style-2 a .store-detail .store-info .store-rating {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 0.75rem;
  line-height: 1;
  background-color: var(--color-background);
  padding: 0.25rem 0.5rem;
  border-radius: 1.25rem;
  margin-top: 0.4375rem;
}
.klb-storebox.style-2 a .store-detail .store-info .store-rating p {
  margin-bottom: 0;
}
.klb-storebox.style-2 a .store-detail .store-info .store-rating p strong {
  font-weight: 600;
}
.klb-storebox.style-2 a .store-detail .store-info .store-rating i {
  font-size: 0.875rem;
  color: var(--color-yellow600);
  margin-right: 0.25rem;
}
.klb-storebox.style-2 a .store-detail .store-info .store-rating i::before {
  margin: 0;
}
.klb-storebox.style-2 a .store-products {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 0.25rem;
     -moz-column-gap: 0.25rem;
          column-gap: 0.25rem;
}
.klb-storebox.style-2 a .store-products .column {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 22%;
  padding: 0.375rem;
  border: 1px solid;
  background-color: var(--color-background);
  border-radius: calc(theme(radius-form) / 2);
  overflow: hidden;
}
body[data-color=custom][data-theme=light] .klb-storebox.style-2 a .store-products .column {
  border-color: rgba(var(--color-rgb), 0.09);
}
body[data-color=default] .klb-storebox.style-2 a .store-products .column, body[data-color=custom][data-theme=dark] .klb-storebox.style-2 a .store-products .column {
  border-color: var(--color-gray100);
}
.klb-storebox.style-2 a .store-products .column .product-price {
  position: absolute;
  padding: 0.25rem 0.375rem;
  border-radius: calc(theme(radius-form) / 2);
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  margin-top: 0.0625rem;
  opacity: 0;
}
body[data-theme=light] .klb-storebox.style-2 a .store-products .column .product-price {
  background-color: rgba(255, 255, 255, 0.8);
}
body[data-theme=dark] .klb-storebox.style-2 a .store-products .column .product-price {
  background-color: rgba(27, 31, 34, 0.8);
}
.klb-storebox.style-2 a .store-products .column .product-price .price {
  font-size: 0.6875rem;
  font-weight: 600;
  line-height: 1;
}
.klb-storebox.style-2 a .store-products .column.product-count {
  font-size: 0.875rem;
  font-weight: 600;
  width: auto;
  border: 0;
  background-color: transparent;
}
.klb-storebox.style-2 a:hover .product-price {
  opacity: 1 !important;
}
.klb-storebox.style-3 .store-detail {
  position: relative;
  border: 1px solid var(--color-gray100);
  background-color: var(--color-background);
  border-radius: var(--theme-radius-base);
  overflow: hidden;
}
.klb-storebox.style-3 .store-detail .store-avatar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.klb-storebox.style-3 .store-detail .store-avatar img {
  max-height: 6.25rem;
}
.klb-storebox.style-3 .store-detail .store-info {
  padding: 0.625rem 0.9375rem;
}
.klb-storebox.style-3 .store-detail .store-info a {
  color: currentColor;
}
.klb-storebox.style-3 .store-detail .store-info a .store-name {
  font-weight: 600;
}
.klb-storebox.style-3 .store-detail .store-info a .store-description {
  font-size: 0.8125rem;
  color: var(--color-gray600);
  margin-bottom: 0.9375rem;
}