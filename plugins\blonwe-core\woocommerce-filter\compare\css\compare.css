@keyframes klbcp-rotate {
  100% {
    transform: rotate(360deg); } 
}

a.klbcp-adding:after {
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -9px;
    transition: opacity .2s ease;
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid rgb(161 161 161 / 40%);
    border-left-color: #000;
    border-radius: 50%;
    vertical-align: middle;
    opacity: 1;
    animation: klbcp-rotate 450ms infinite linear;
    left: 50%;
    margin-left: -9px;
}

.klbcp-adding:before {
    opacity: 0;
}

.klbcp-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999989;
    background: rgba(0, 0, 0, 0.7);
    opacity: 0;
    font-size: 14px;
    visibility: hidden;
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    box-sizing: border-box;
}

.klbcp-message.klbcp-show {
    opacity: 1;
    visibility: visible;
}

.klbcp-message-inner {
    width: 100%;
    max-width: 360px;
    height: auto;
    max-height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    -webkit-transform: translate3d(-50%, -50%, 0);
    display: flex;
    flex-direction: column;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    overflow: hidden;
    padding: 40px;
    background-color: #fff;
    text-align: center;
}

.klbcp-message a.btn {
    justify-content: center;
    margin-top: 10px;
}

.woocommerce-MyAccount-navigation-link--compare a:before {
    font-family: "klbtheme";
    font-style: normal;
    font-weight: normal;
    speak: never;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: 0.2em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: 0.2em;
    font-size: 120%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\ec85';
    text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3);
    font-size: 1.25rem;
}


a.klbcp-btn:before {
    font-family: "klbtheme";
    font-style: normal;
    font-weight: normal;
    speak: never;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: 0.2em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: 0.2em;
    font-size: 120%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\ec85';
    text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3);
    font-size: 1.25rem;
}

a.klbcp-btn.klbcp-btn-added:before {
    content: '\e8ef';
}

span.klbcp-remove {
	cursor: pointer;
}

.klbcp_table td:first-child {
    border-left: 0;
}

.klbcp_table tr:first-child td {
    border-top: none;
}

.klbcp_table .product-rating {
    display: table-row;
}

.klbcp_table td:last-child {
    border-right: 0;
}

.klbcp_table .product-add_to_cart p {
    margin: 0;
}

.klbcp_table .product-add_to_cart a.button {
    font-size: 0.875rem;
    line-height: 2.1875rem;
    height: 2.1875rem;
    color: #FFF;
    background-color: var(--theme-primary-color);
}

.klbcp_table .product-add_to_cart a.button:hover {
    background-color: rgba(var(--theme-primary-color-RGB), 0.9);
}

.klbcp-list .cart-empty-page .empty-icon {
    font-size: 7rem;
    margin-bottom: 0;
}


table.klbcp_table {
    table-layout: fixed;
    display: table;
    width: 100%;
}

.klbcp-list.compare-page {
    overflow-x: auto;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
}

.klbcp_table td.td-label {
    width: 120px;
}

.klbcp_table td:not(.td-label) {
    width: 14.375rem;
}


