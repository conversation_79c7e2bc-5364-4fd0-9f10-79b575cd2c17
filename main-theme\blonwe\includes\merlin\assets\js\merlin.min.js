var Merlin=function(e){var n={install_child:function(n){(new function(){var n,i=e(".merlin__body"),t=e("#child-theme-text");function s(e){void 0!==e.done?(setTimeout(function(){t.addClass("lead")},0),setTimeout(function(){t.addClass("success"),t.html(e.message)},600),n()):(t.addClass("lead error"),t.html(e.error))}return{init:function(t){n=function(){setTimeout(function(){e(".merlin__body").addClass("js--finished")},1500),i.removeClass(drawer_opened),setTimeout(function(){e(".merlin__body").addClass("exiting")},3500),setTimeout(function(){window.location.href=t.href},4e3)},jQuery.post(merlin_params.ajaxurl,{action:"merlin_child_theme",wpnonce:merlin_params.wpnonce},s).fail(s)}}}).init(n)},activate_license:function(n){(new function(){var n,i=e(".merlin__body"),t=e(".merlin__content--license-key"),s=e("#license-text");function o(i){void 0!==i.success&&i.success?(s.siblings(".error-message").remove(),setTimeout(function(){s.addClass("lead")},0),setTimeout(function(){s.addClass("success"),s.html(i.message)},600),n()):(e(".js-merlin-license-activate-button").removeClass("merlin__button--loading").data("done-loading","no"),s.siblings(".error-message").remove(),t.addClass("has-error"),s.html(i.message),s.siblings(".error-message").addClass("lead error"))}return{init:function(s){n=function(){setTimeout(function(){e(".merlin__body").addClass("js--finished")},1500),i.removeClass(drawer_opened),setTimeout(function(){e(".merlin__body").addClass("exiting")},3500),setTimeout(function(){window.location.href=s.href},4e3)},t.removeClass("has-error"),jQuery.post(merlin_params.ajaxurl,{action:"merlin_activate_license",wpnonce:merlin_params.wpnonce,license_key:e(".js-license-key").val()},o).fail(o)}}}).init(n)},install_plugins:function(n){(new function(){var n,i,t=e(".merlin__body"),s=0,o="",r="";function a(e){var n=i.find("label");"object"==typeof e&&void 0!==e.message?(n.removeClass("installing success error").addClass(e.message.toLowerCase()),void 0!==e.done&&e.done?d():void 0!==e.url?e.hash==r?(n.removeClass("installing success").addClass("error"),d()):(r=e.hash,jQuery.post(e.url,e,a).fail(a)):d()):l()}function l(){if(o){var e=i.find("input:checkbox");e.is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_plugins",wpnonce:merlin_params.wpnonce,slug:o},a).fail(a):(i.addClass("skipping"),setTimeout(d,300))}}function d(){i&&(i.data("done_item")||(s++,i.data("done_item",1)),i.find(".spinner").css("visibility","hidden"));var t=e(".merlin__drawer--install-plugins li");t.each(function(){var n=e(this);return!!n.data("done_item")||(o=n.data("slug"),i=n,l(),!1)}),s>=t.length&&n()}return{init:function(i){e(".merlin__drawer--install-plugins").addClass("installing"),e(".merlin__drawer--install-plugins").find("input").prop("disabled",!0),n=function(){setTimeout(function(){e(".merlin__body").addClass("js--finished")},1e3),t.removeClass(drawer_opened),setTimeout(function(){e(".merlin__body").addClass("exiting")},3e3),setTimeout(function(){window.location.href=i.href},3500)},d()}}}).init(n)},install_content:function(n){(new function(){var n,i,t,s=e(".merlin__body"),o=0,r="",a="",l=1,d=0;function c(n){var t=i.find("label");"object"==typeof n&&void 0!==n.message?(t.addClass(n.message.toLowerCase()),void 0!==n.num_of_imported_posts&&0<d&&(l="all"===n.num_of_imported_posts?d:n.num_of_imported_posts,u()),void 0!==n.url?n.hash===a?(t.addClass("status--failed"),m()):(a=n.hash,void 0===n.selected_index&&(n.selected_index=e(".js-merlin-demo-import-select").val()||0),jQuery.post(n.url,n,c).fail(c)):(n.done,m())):(console.log(n),t.addClass("status--error"),m())}function m(){var t=!1;i&&(i.data("done_item")||(o++,i.data("done_item",1)),i.find(".spinner").css("visibility","hidden"));var s=e(".merlin__drawer--import-content__list-item");e(".merlin__drawer--import-content__list-item input:checked");s.each(function(){""==r||t?(r=e(this).data("content"),i=e(this),function(){if(r){var n=i.find("input:checkbox");n.is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_content",wpnonce:merlin_params.wpnonce,content:r,selected_index:e(".js-merlin-demo-import-select").val()||0},c).fail(c):(i.addClass("skipping"),setTimeout(m,300))}}(),t=!1):e(this).data("content")==r&&(t=!0)}),o>=s.length&&n()}function u(){e(".js-merlin-progress-bar").css("width",l/d*100+"%");var n,i,s,o=(n=l/d*100,i=0,s=99,Math.min(s,Math.max(i,n)));e(".js-merlin-progress-bar-percentage").html(Math.round(o)+"%"),1==l/d&&clearInterval(t)}return{init:function(i){e(".merlin__drawer--import-content").addClass("installing"),e(".merlin__drawer--import-content").find("input").prop("disabled",!0),n=function(){e.post(merlin_params.ajaxurl,{action:"merlin_import_finished",wpnonce:merlin_params.wpnonce,selected_index:e(".js-merlin-demo-import-select").val()||0}),setTimeout(function(){e(".js-merlin-progress-bar-percentage").html("100%")},100),setTimeout(function(){s.removeClass(drawer_opened)},500),setTimeout(function(){e(".merlin__body").addClass("js--finished")},1500),setTimeout(function(){e(".merlin__body").addClass("exiting")},3400),setTimeout(function(){window.location.href=i.href},4e3)},function(){if(!e(".merlin__drawer--import-content__list-item .checkbox-content").is(":checked"))return!1;jQuery.post(merlin_params.ajaxurl,{action:"merlin_get_total_content_import_items",wpnonce:merlin_params.wpnonce,selected_index:e(".js-merlin-demo-import-select").val()||0},function(e){0<(d=e.data)&&(u(),t=setInterval(function(){l+=d/500,u()},1e3))})}(),m()}}}).init(n)}};function i(){var i=e(".merlin__body"),t=(e(".merlin__body--loading"),e(".merlin__body--exiting"),e("#merlin__drawer-trigger"));drawer_opened="merlin__drawer--open",setTimeout(function(){i.addClass("loaded")},100),t.on("click",function(){i.toggleClass(drawer_opened)}),e(".merlin__button--proceed:not(.merlin__button--closer)").click(function(e){e.preventDefault();var n=this.getAttribute("href");i.addClass("exiting"),setTimeout(function(){window.location=n},400)}),e(".merlin__button--closer").on("click",function(e){i.removeClass(drawer_opened),e.preventDefault();var n=this.getAttribute("href");setTimeout(function(){i.addClass("exiting")},600),setTimeout(function(){window.location=n},1100)}),e(".button-next").on("click",function(i){if(i.preventDefault(),!function(e){var n=jQuery(e);if("yes"==n.data("done-loading"))return!1;n.is("input")||n.is("button");return n.data("done-loading","yes"),n.addClass("merlin__button--loading"),{done:function(){!0,n.attr("disabled",!1)}}}(this))return!1;var t=e(this).data("callback");return!t||void 0===n[t]||(n[t](this),!1)}),e(document).on("change",".js-merlin-demo-import-select",function(){var n=e(this).val();e(".js-merlin-select-spinner").show(),e.post(merlin_params.ajaxurl,{action:"merlin_update_selected_import_data_info",wpnonce:merlin_params.wpnonce,selected_index:n},function(n){n.success?e(".js-merlin-drawer-import-content").html(n.data):alert(merlin_params.texts.something_went_wrong),e(".js-merlin-select-spinner").hide()}).fail(function(){e(".js-merlin-select-spinner").hide(),alert(merlin_params.texts.something_went_wrong)})})}return{init:function(){this,e(i)},callback:function(e){console.log(e),console.log(this)}}}(jQuery);Merlin.init();