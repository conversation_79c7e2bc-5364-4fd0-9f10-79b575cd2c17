# Temporary .htaccess settings for demo import
# Copy these settings to your main .htaccess file during import

# Increase memory limit
php_value memory_limit 512M

# Remove execution time limit
php_value max_execution_time 0

# Increase input variables limit
php_value max_input_vars 10000

# Increase post max size
php_value post_max_size 64M

# Increase upload max filesize
php_value upload_max_filesize 64M

# Increase max file uploads
php_value max_file_uploads 100

# Enable compression to reduce memory usage
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
