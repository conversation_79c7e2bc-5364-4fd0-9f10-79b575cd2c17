/* ----- gdpr notice ----- */
.site-gdpr {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  color: #FFF;
  border: 1px solid;
  background-color: var(--color-text);
  -webkit-box-shadow: rgba(27, 31, 34, 0.05) 0px 1px 3px, rgba(27, 31, 34, 0.05) 0px 28px 23px -7px, rgba(27, 31, 34, 0.04) 0px 12px 12px -7px;
          box-shadow: rgba(27, 31, 34, 0.05) 0px 1px 3px, rgba(27, 31, 34, 0.05) 0px 28px 23px -7px, rgba(27, 31, 34, 0.04) 0px 12px 12px -7px;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  opacity: 0;
  padding: 1.25rem;
  z-index: 999;
}
body[data-color=custom] .site-gdpr {
  border-color: rgba(var(--color-rgb), 0.1);
}
body[data-color=default] .site-gdpr {
  border-color: var(--color-gray100);
}
body[data-theme=dark] .site-gdpr, body[data-color=custom][data-theme=dark] .site-gdpr {
  background-color: var(--color-gray200);
  border-color: var(--color-gray200);
}
@media screen and (min-width: 992px) {
  .site-gdpr {
    max-width: 21.25rem;
    bottom: 1.875rem;
    left: 1.875rem;
    border-radius: theme(radius-base);
    padding: 1.5625rem;
  }
}
.site-gdpr .gdpr-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (min-width: 992px) {
  .site-gdpr .gdpr-inner {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
.site-gdpr .gdpr-inner .entry-title {
  font-size: 0.875rem;
}
.site-gdpr .gdpr-inner p {
  font-size: 0.75rem;
}
.site-gdpr .gdpr-inner .btn {
  font-size: 0.8125rem;
  line-height: 2.25rem;
  height: 2.25rem;
}

@media screen and (max-width: 64rem) {
  .site-gdpr.mobile-menu-active {
    bottom: 74px;
  }
}