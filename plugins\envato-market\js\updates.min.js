window.wp=window.wp||{},function(i,u){"use strict";u.envato={},u.envato.ajaxNonce=window._wpUpdatesSettings.ajax_nonce,u.envato.shouldRequestFilesystemCredentials=null,u.envato.filesystemCredentials={ftp:{host:null,username:null,password:null,connectionType:null},ssh:{publicKey:null,privateKey:null}},u.envato.updateLock=!1,u.envato.updateDoneSuccessfully=!1,u.envato.updateQueue=[],u.envato.$elToReturnFocusToFromCredentialsModal=null,u.envato.decrementCount=function(e){var a,t=i("#wp-admin-bar-updates .ab-label"),n=i('a[href="update-core.php"] .update-plugins'),o=i("#menu-plugins"),s=t.text();(s=parseInt(s,10)-1)<0||isNaN(s)||(i("#wp-admin-bar-updates .ab-item").removeAttr("title"),t.text(s),n.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+s)}),n.removeAttr("title"),n.find(".update-count").text(s),"plugin"===e&&(a=o.find(".plugin-count").eq(0).text(),(a=parseInt(a,10)-1)<0||isNaN(a)||(o.find(".plugin-count").text(a),o.find(".update-plugins").each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+a)}),0<a?i(".subsubsub .upgrade .count").text("("+a+")"):i(".subsubsub .upgrade").remove())))},u.envato.updatePlugin=function(e,t){var a=i(".envato-card-"+t).find(".update-now"),n=a.data("name"),n=u.i18n.sprintf(u.i18n.__("Updating %s...","envato-market"),n);a.attr("aria-label",n),a.addClass("updating-message"),a.html()!==n&&a.data("originaltext",a.html()),a.text(n),u.envato.updateLock?u.envato.updateQueue.push({type:"update-plugin",data:{plugin:e,slug:t}}):(u.envato.updateLock=!0,a={_ajax_nonce:u.envato.ajaxNonce,plugin:e,slug:t,username:u.envato.filesystemCredentials.ftp.username,password:u.envato.filesystemCredentials.ftp.password,hostname:u.envato.filesystemCredentials.ftp.hostname,connection_type:u.envato.filesystemCredentials.ftp.connectionType,public_key:u.envato.filesystemCredentials.ssh.publicKey,private_key:u.envato.filesystemCredentials.ssh.privateKey},u.ajax.post("update-plugin",a).done(u.envato.updateSuccess).fail(u.envato.updateError))},u.envato.updateTheme=function(e){var t=i(".envato-card-"+e).find(".update-now"),a=t.data("name"),a=u.i18n.sprintf(u.i18n.__("Updating %s...","envato-market"),a);t.attr("aria-label",a),t.addClass("updating-message"),t.html()!==a&&t.data("originaltext",t.html()),t.text(a),u.envato.updateLock?u.envato.updateQueue.push({type:"update-theme",data:{theme:e}}):(u.envato.updateLock=!0,t={_ajax_nonce:u.envato.ajaxNonce,theme:e,slug:e,username:u.envato.filesystemCredentials.ftp.username,password:u.envato.filesystemCredentials.ftp.password,hostname:u.envato.filesystemCredentials.ftp.hostname,connection_type:u.envato.filesystemCredentials.ftp.connectionType,public_key:u.envato.filesystemCredentials.ssh.publicKey,private_key:u.envato.filesystemCredentials.ssh.privateKey},u.ajax.post("update-theme",t).done(u.envato.updateSuccess).fail(u.envato.updateError))},u.envato.updateSuccess=function(e){var t=i(".envato-card-"+e.slug),a=t.find(".column-update"),n=t.find(".update-now"),t=t.find(".version"),o=n.data("name"),s=n.data("version"),s=t.attr("aria-label").replace("%s",s),o=(n.addClass("disabled"),u.i18n.sprintf(u.i18n.__("Updating %s...","envato-market"),o));n.attr("aria-label",o),t.text(s),n.removeClass("updating-message").addClass("updated-message"),n.text(u.i18n.__("Updated!","envato-market")),u.a11y.speak(o),a.addClass("update-complete").delay(1e3).fadeOut(),u.envato.decrementCount("plugin"),u.envato.updateDoneSuccessfully=!0,u.envato.updateLock=!1,i(document).trigger("envato-update-success",e),u.envato.queueChecker()},u.envato.updateError=function(e){var t;u.envato.updateDoneSuccessfully=!1,e.errorCode&&"unable_to_connect_to_filesystem"===e.errorCode&&u.envato.shouldRequestFilesystemCredentials?u.envato.credentialError(e,"update-plugin"):((t=i(".envato-card-"+e.slug).find(".update-now")).data("name"),t.attr("aria-label",u.i18n.__("Updating failed","envato-market")),t.removeClass("updating-message"),t.html(u.i18n.sprintf(u.i18n.__("Updating failed %s...","envato-market"),"string"!==e.errorMessage?e.errorMessage:e.error)),u.envato.updateLock=!1,i(document).trigger("envato-update-error",e),u.envato.queueChecker())},u.envato.showErrorInCredentialsForm=function(e){var t=i(".notification-dialog");t.find(".error").remove(),t.find("h3").after('<div class="error">'+e+"</div>")},u.envato.credentialError=function(e,t){u.envato.updateQueue.push({type:t,data:{plugin:e.plugin,slug:e.slug}}),u.envato.showErrorInCredentialsForm(e.error),u.envato.requestFilesystemCredentials()},u.envato.queueChecker=function(){var e;u.envato.updateLock||u.envato.updateQueue.length<=0||(e=u.envato.updateQueue.shift(),u.envato.updatePlugin(e.data.plugin,e.data.slug))},u.envato.requestFilesystemCredentials=function(e){!1===u.envato.updateDoneSuccessfully&&(u.envato.$elToReturnFocusToFromCredentialsModal=i(e.target),u.envato.updateLock=!0,u.envato.requestForCredentialsModalOpen())},u.envato.keydown=function(e){27===e.keyCode?u.envato.requestForCredentialsModalCancel():9===e.keyCode&&("upgrade"!==e.target.id||e.shiftKey?"hostname"===e.target.id&&e.shiftKey&&(i("#upgrade").focus(),e.preventDefault()):(i("#hostname").focus(),e.preventDefault()))},u.envato.requestForCredentialsModalOpen=function(){var e=i("#request-filesystem-credentials-dialog");i("body").addClass("modal-open"),e.show(),e.find("input:enabled:first").focus(),e.keydown(u.envato.keydown)},u.envato.requestForCredentialsModalClose=function(){i("#request-filesystem-credentials-dialog").hide(),i("body").removeClass("modal-open"),u.envato.$elToReturnFocusToFromCredentialsModal.focus()},u.envato.requestForCredentialsModalCancel=function(){var e;!1===u.envato.updateLock&&0===u.envato.updateQueue.length||(e=u.envato.updateQueue[0].data.slug,u.envato.updateLock=!1,u.envato.updateQueue=[],u.envato.requestForCredentialsModalClose(),(e=i(".envato-card-"+e).find(".update-now")).removeClass("updating-message"),e.html(e.data("originaltext")))},u.envato.beforeunload=function(){if(u.envato.updateLock)return u.i18n.__("Update in progress, really leave?","envato-market")},i(document).ready(function(){u.envato.shouldRequestFilesystemCredentials=!(i("#request-filesystem-credentials-dialog").length<=0),i("#request-filesystem-credentials-dialog form").on("submit",function(){return u.envato.filesystemCredentials.ftp.hostname=i("#hostname").val(),u.envato.filesystemCredentials.ftp.username=i("#username").val(),u.envato.filesystemCredentials.ftp.password=i("#password").val(),u.envato.filesystemCredentials.ftp.connectionType=i('input[name="connection_type"]:checked').val(),u.envato.filesystemCredentials.ssh.publicKey=i("#public_key").val(),u.envato.filesystemCredentials.ssh.privateKey=i("#private_key").val(),u.envato.requestForCredentialsModalClose(),u.envato.updateLock=!1,u.envato.queueChecker(),!1}),i('#request-filesystem-credentials-dialog [data-js-action="close"], .notification-dialog-background').on("click",function(){u.envato.requestForCredentialsModalCancel()}),i('#request-filesystem-credentials-dialog input[name="connection_type"]').on("change",function(){i(this).parents("form").find("#private_key, #public_key").parents("label").toggle("ssh"===i(this).val())}).change(),i(".envato-card.plugin").on("click",".update-now",function(e){var t=i(e.target);e.preventDefault(),u.envato.shouldRequestFilesystemCredentials&&!u.envato.updateLock&&u.envato.requestFilesystemCredentials(e),u.envato.updatePlugin(t.data("plugin"),t.data("slug"))}),i(".envato-card.theme").on("click",".update-now",function(e){var t=i(e.target);e.preventDefault(),u.envato.shouldRequestFilesystemCredentials&&!u.envato.updateLock&&u.envato.requestFilesystemCredentials(e),u.envato.updateTheme(t.data("slug"))}),i("#plugin_update_from_iframe").on("click",function(e){var t=window.parent===window?null:window.parent;i.support.postMessage=!!window.postMessage,!1!==i.support.postMessage&&null!==t&&-1===window.parent.location.pathname.indexOf("update-core.php")&&(e.preventDefault(),e={action:"updatePlugin",slug:i(this).data("slug")},t.postMessage(JSON.stringify(e),window.location.origin))})}),i(window).on("message",function(t){t=t.originalEvent;let a;var e=document.location,e=e.protocol+"//"+e.hostname;if(t.origin===e&&t.data){try{a=i.parseJSON(t.data)}catch(e){a=t.data}try{if(void 0===a.action)return}catch(e){}try{switch(a.action){case"decrementUpdateCount":u.envato.decrementCount(a.upgradeType);break;case"updatePlugin":tb_remove(),i(".envato-card-"+a.slug).find("h4 a").focus(),i(".envato-card-"+a.slug).find('[data-slug="'+a.slug+'"]').trigger("click")}}catch(e){}}}),i(window).on("beforeunload",u.envato.beforeunload)}(jQuery,window.wp,window.ajaxurl);