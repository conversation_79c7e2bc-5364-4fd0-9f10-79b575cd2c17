.wishlist-button {
	position:relative;
}

a.klbwl-btn:before{
	font-family: "klbtheme";
    font-size: 1.375rem;
    content: "\eb35";
    margin-right: 0.375rem;
}

.products .wishlist-button a {
    font-size: 0;
	color: currentColor;
}

.products a.klbwl-btn:before {
    margin-right: 0;
}

a.klbwl-btn.klbwl-product-in-list:before{
    content: '\ec57' !important;
}

@keyframes klb-wishlist-rotate {
  100% {
    transform: rotate(360deg); } 
}

a.klbwl-btn.klbwl-adding:after,
span.klbwl-removing i:before {
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -9px;
    transition: opacity .2s ease;
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid rgb(161 161 161 / 40%);
    border-left-color: #000;
    border-radius: 50%;
    vertical-align: middle;
    opacity: 1;
    animation: klb-wishlist-rotate 450ms infinite linear;
}

.products a.klbwl-btn.klbwl-adding:after {
    left: 50%;
    margin-left: -9px;
}

.wishlist-button .klbwl-btn.klbwl-adding:before {
    opacity: 0;
}

.klbwl-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999989;
    background: rgba(0, 0, 0, 0.7);
    opacity: 0;
    font-size: 14px;
    visibility: hidden;
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    box-sizing: border-box;
}

.klbwl-popup .klbwl-popup-inner {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
}

.klbwl-popup .klbwl-popup-inner .klbwl-popup-content {
    width: 100%;
    max-width: 360px;
    height: auto;
    max-height: 100%;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    -webkit-transform: translate3d(-50%, -50%, 0);
    display: flex;
    flex-direction: column;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    overflow: hidden;
    padding: 40px;
    background-color: #fff;
	text-align: center;
}

.klbwl-popup.klbwl-show {
    opacity: 1;
    visibility: visible;
}

.klbwl-popup.klbwl-show .klbwl-popup-inner .klbwl-popup-content {
    top: 50%;
}

.klbwl-popup-content a.btn {
    width: 100%;
	justify-content: center;
}

.klbwl-popup-content a.btn + a.btn {
    margin-top: 10px;
}

.klbwl-popup-content .klbwl-notice {
    margin-bottom: 10px;
}

td.klbwl-item--remove span {
	display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-family: fantasy;
    font-size: 0.9375rem;
    font-weight: 700;
    line-height: 1;
    width: 1.125rem;
    height: 1.125rem;
    color: #FFF;
    background-color: var(--color-red600);
    border-radius: 50%;
    cursor: pointer;
	position: relative;
}

td.klbwl-item--remove span.klbwl-removing {
    background: none;
}

span.klbwl-removing i:before {
    margin-left: 0;
    margin-top: 0;
    top: 0;
}

td.klbwl-item--image img {
    max-width: 65px;
}

td.klbwl-item--addtocart p {
    margin-bottom: 0;
	white-space: nowrap;
}

td.klbwl-item--addtocart a i {
    display: none;
}

td.klbwl-item--name a {
    color: currentColor;
    font-weight: 500;
}

.klbwl-no-result .cart-empty-page .empty-icon {
    font-size: 7rem;
    margin-bottom: 0;
}

.klbwl-no-result + .klbwl-actions {
    display: none;
}


@media(max-width: 480px){
	.klbwl-list td.klbwl-item--stock,
	.klbwl-list th.product-date,
	.klbwl-list th.product-price,
	.klbwl-list th.product-subtotal,
	.klbwl-list td.klbwl-item--date,
	.klbwl-list td.klbwl-item--price {
	    display: none;
	}
	
	
	.klbwl-list td.klbwl-item--addtocart a.button {
		padding: 5px;
		height: auto;
		font-size: 12px;
	}
	
	.klbwl-list.woocommerce-cart-form table th.product-remove {
	    display: table-cell !important;
	}
}