# حل مشاكل استيراد البيانات التجريبية

## المشكلة الشائعة
عند استيراد البيانات التجريبية (مثل Marketplace) يظهر خطأ "كان هناك خطأ فادح في هذا الموقع" عند الوصول لأكثر من 50%.

## الحلول المتدرجة

### الحل الأول: تحسين إعدادات الخادم
1. أضف هذه الأكواد إلى ملف `wp-config.php`:
```php
// زيادة الذاكرة
ini_set('memory_limit', '512M');
define('WP_MEMORY_LIMIT', '512M');

// إزالة حد وقت التنفيذ
ini_set('max_execution_time', 0);
set_time_limit(0);

// زيادة متغيرات الإدخال
ini_set('max_input_vars', '10000');
```

### الحل الثاني: تحديث ملف .htaccess
انسخ محتوى ملف `.htaccess-import` إلى ملف `.htaccess` الرئيسي مؤقتاً أثناء الاستيراد.

### الحل الثالث: الاستيراد التدريجي
1. ابدأ بـ Electronic (أصغر حجماً)
2. ثم جرب Fashion أو Grocery
3. أخيراً جرب Marketplace

### الحل الرابع: استيراد يدوي
إذا فشل الاستيراد التلقائي:

1. **استيراد المحتوى يدوياً:**
   - اذهب إلى `أدوات > استيراد`
   - اختر WordPress
   - ارفع ملف `marketplace-content.xml`

2. **استيراد الويدجت يدوياً:**
   - استخدم plugin "Widget Importer & Exporter"
   - ارفع ملف `marketplace-widgets.wie`

3. **استيراد إعدادات المخصص:**
   - استخدم plugin "Customizer Export/Import"
   - ارفع ملف `marketplace-customizer.dat`

### الحل الخامس: تقسيم ملف XML
إذا كان ملف XML كبيراً جداً:

1. استخدم أداة تقسيم XML
2. قسم الملف إلى أجزاء أصغر
3. استورد كل جزء منفصلاً

### الحل السادس: استخدام WP-CLI
إذا كان لديك وصول للخادم:
```bash
wp import marketplace-content.xml --authors=create
```

## فحص المتطلبات
قبل الاستيراد، تأكد من:
- ✅ الذاكرة: 512MB أو أكثر
- ✅ وقت التنفيذ: غير محدود أو 300 ثانية+
- ✅ متغيرات الإدخال: 5000 أو أكثر
- ✅ حجم الرفع: 64MB أو أكثر

## نصائح إضافية
1. **أغلق البلاجينات غير الضرورية** أثناء الاستيراد
2. **استخدم استضافة قوية** للاستيراد
3. **اعمل نسخة احتياطية** قبل الاستيراد
4. **جرب في وقت قليل الزحام** على الخادم
5. **استخدم اتصال إنترنت مستقر**

## إذا استمرت المشكلة
1. تحقق من ملف `error_log` في مجلد WordPress
2. فعّل وضع Debug في `wp-config.php`
3. تواصل مع مزود الاستضافة لزيادة الحدود
4. جرب استيراد البيانات على خادم محلي أولاً

## ملفات مهمة تم إضافتها
- `import-helper.php` - تحسينات تلقائية للاستيراد
- `.htaccess-import` - إعدادات خادم محسنة
- تحسينات في `class-merlin.php` لزيادة الذاكرة ووقت التنفيذ
